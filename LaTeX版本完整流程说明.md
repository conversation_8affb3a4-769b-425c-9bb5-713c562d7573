# LaTeX版本LADE算法完整流程说明

## 完成的工作

我已经将完整详细的LADE算法流程伪代码写入到LaTeX文件 `第三部分_正确版本.tex` 中。

## 主要改进内容

### 1. 完整的主算法流程 (第518-630行)

**算法标题**: LADE算法主流程

**输入参数**:
- 网络图G=(V,E)
- 种子数k
- 种群大小NP  
- 最大函数评估次数FEs_max
- 最大迭代数G_max
- 激活概率p
- 采样数SN

**输出**: 最优种子集合S*

**详细流程包含5个阶段**:

#### 阶段1：预处理 (步骤1-6)
```latex
\STATE $bridge\_nodes \leftarrow \text{detect\_bridge\_nodes}(G)$ \COMMENT{使用介数中心性检测桥节点}
\STATE $combined\_scores \leftarrow \text{calculate\_combined\_centrality}(G)$ \COMMENT{计算综合中心性评分}
\STATE $G \leftarrow \text{convert\_node\_labels\_to\_integers}(G)$ \COMMENT{节点ID连续化}
\FOR{每个节点$v \in V$}
    \STATE $LFV\_cache[v] \leftarrow \text{compute\_LFV}(v, G, p)$ \COMMENT{预计算LFV值}
\ENDFOR
```

#### 阶段2：混合初始化 (步骤7-18)
```latex
\STATE $regions \leftarrow \text{divide\_by\_diameter}(G)$ \COMMENT{基于直径路径划分区域}
\STATE $\mathcal{S}_{LHS} \leftarrow \text{sample\_lhs}(G, k, SN/2, bridge\_nodes, combined\_scores)$
\STATE $\mathcal{S}_{score} \leftarrow \text{sample\_score}(G, k, SN/2)$ \COMMENT{度中心性采样}
\STATE $all\_solutions \leftarrow \mathcal{S}_{LHS} \cup \mathcal{S}_{score}$ \COMMENT{合并候选解}
\STATE $fitness\_cache \leftarrow \text{parallel\_compute\_fitness}(all\_solutions, G, p)$ \COMMENT{并行计算适应度}
\STATE $high\_quality \leftarrow \text{quality\_filter}(all\_solutions, fitness\_cache, 0.7 \times |all\_solutions|)$
\STATE $diverse\_solutions \leftarrow \text{diversity\_filter}(high\_quality, \theta=0.8)$ \COMMENT{多样性筛选}
\STATE $P^{(0)} \leftarrow \text{initialize\_population\_hybrid}(diverse\_solutions, NP)$ \COMMENT{构建初始种群}
```

#### 阶段3：参数初始化 (步骤19-24)
```latex
\STATE $\mu_{CR} \leftarrow 0.5$，$\mu_F \leftarrow 0.5$，$c \leftarrow 0.1$ \COMMENT{参数分布初始化}
\STATE $\Lambda_{history} \leftarrow \emptyset$，$g \leftarrow 0$，$FEs \leftarrow 0$ \COMMENT{计数器初始化}
\STATE $\lambda^{(0)} \leftarrow \text{compute\_lambda}(P^{(0)})$ \COMMENT{计算初始地形状态值}
\STATE $\Lambda_{history} \leftarrow \Lambda_{history} \cup \{\lambda^{(0)}\}$
\STATE $stagnation\_counter \leftarrow 0$ \COMMENT{停滞计数器初始化}
```

#### 阶段4：主进化循环 (步骤25-112)
包含以下子步骤：

**地形状态分析**:
```latex
\STATE $\lambda^{(g)} \leftarrow \text{compute\_lambda}(P^{(g-1)})$ \COMMENT{计算当前地形状态值}
\STATE $\Lambda_{history} \leftarrow \Lambda_{history} \cup \{\lambda^{(g)}\}$
\STATE $current\_state \leftarrow \text{determine\_state}(\lambda^{(g)}, \Lambda_{history}, g)$ \COMMENT{确定搜索状态}
```

**种群进化** (包含参数生成、变异、交叉、选择):
```latex
\STATE $CR_i \leftarrow \mathcal{N}(\mu_{CR}, 0.1^2)$ \COMMENT{截断到$[0,1]$}
\STATE $F_i \leftarrow \text{Cauchy}(\mu_F, 0.1)$ \COMMENT{截断到$[0,1]$}
```

**四种变异策略**:
- convergence: DE/best/1
- exploitation: DE/current-to-best/1  
- exploration: DE/rand/1
- escape: 逃逸变异

**局部搜索**:
```latex
\STATE $num\_to\_optimize \leftarrow \max(1, \lfloor 0.1 \times NP \rfloor)$ \COMMENT{选择前10\%个体}
\STATE $candidates \leftarrow \text{top\_individuals}(new\_population, num\_to\_optimize)$
```

**参数更新**:
```latex
\STATE $\mu_{CR} \leftarrow (1-c) \cdot \mu_{CR} + c \cdot \text{mean}(S_{CR})$
\STATE $\mu_F \leftarrow (1-c) \cdot \mu_F + c \cdot \text{mean}(S_F)$
```

#### 阶段5：返回结果 (步骤113)
```latex
\RETURN $S^* \leftarrow \arg\max_{X \in P^{(g)}} \text{LIE}(X)$ \COMMENT{返回最优解}
```

### 2. 关键子算法详细描述 (第632-725行)

#### 算法1：地形状态值计算
- **功能**: 计算基于加权PDI距离的地形状态值λ
- **关键步骤**: 
  - 计算所有个体间的加权PDI距离矩阵
  - 使用LFV值作为权重
  - 计算最优个体的平均距离

#### 算法2：搜索状态判断  
- **功能**: 基于λ值和历史信息确定搜索状态
- **四种状态**: convergence, exploitation, exploration, escape
- **动态阈值**: 使用四分位数Q1, Q3

#### 算法3：基于LFV的局部搜索
- **功能**: 对个体进行基于LFV值的邻域搜索优化
- **策略**: 优先替换低LFV值的节点
- **参数**: max_neighbors控制搜索范围

### 3. 算法复杂度分析 (第726-777行)

#### 时间复杂度
```latex
T(n,k,NP,G_{max}) = O(n^2 + G_{max} \cdot NP \cdot (NP \cdot k + k \cdot \bar{d}))
```

#### 空间复杂度  
```latex
O(NP^2 + NP \cdot k + n + m + C)
```

### 4. 本章小结 (第779-807行)

详细总结了算法的7个关键组件和4个主要创新点。

## LaTeX格式特点

### 1. 规范的算法环境
- 使用 `\begin{algorithm}[H]` 和 `\begin{algorithmic}[1]`
- 自动行号编排
- 清晰的输入输出说明

### 2. 详细的注释
- 每个关键步骤都有 `\COMMENT{}` 注释
- 解释算法的设计意图和实现细节

### 3. 数学公式规范
- 使用标准的LaTeX数学符号
- 正确的集合运算和函数表示

### 4. 结构化组织
- 明确的阶段划分
- 逻辑清晰的步骤编排
- 完整的算法流程覆盖

## 文件信息

- **文件名**: `第三部分_正确版本.tex`
- **总行数**: 810行
- **主算法**: 第518-630行 (113行详细伪代码)
- **子算法**: 第632-725行 (94行关键算法)
- **复杂度分析**: 第726-777行
- **格式**: 标准LaTeX学术论文格式

这个版本的LADE算法流程伪代码是完整、详细且准确的，完全基于您的实际代码实现，可以直接用于学术论文的撰写。
