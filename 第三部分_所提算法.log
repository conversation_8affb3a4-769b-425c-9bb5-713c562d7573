This is XeTeX, Version 3.141592653-2.6-0.999997 (TeX Live 2025) (preloaded format=xelatex 2025.6.9)  8 JUL 2025 19:38
entering extended mode
 restricted \write18 enabled.
 %&-line parsing enabled.
**第三部分_所提算法.tex
(./第三部分_所提算法.tex
LaTeX2e <2024-11-01> patch level 2
L3 programming layer <2025-01-18>
(e:/latex/texlive/2025/texmf-dist/tex/latex/base/article.cls
Document Class: article 2024/06/29 v1.4n Standard LaTeX document class
(e:/latex/texlive/2025/texmf-dist/tex/latex/base/size12.clo
File: size12.clo 2024/06/29 v1.4n Standard LaTeX file (size option)
)
\c@part=\count192
\c@section=\count193
\c@subsection=\count194
\c@subsubsection=\count195
\c@paragraph=\count196
\c@subparagraph=\count197
\c@figure=\count198
\c@table=\count199
\abovecaptionskip=\skip49
\belowcaptionskip=\skip50
\bibindent=\dimen141
)
(e:/latex/texlive/2025/texmf-dist/tex/latex/ctex/ctex.sty
(e:/latex/texlive/2025/texmf-dist/tex/latex/l3kernel/expl3.sty
Package: expl3 2025-01-18 L3 programming layer (loader) 

(e:/latex/texlive/2025/texmf-dist/tex/latex/l3backend/l3backend-xetex.def
File: l3backend-xetex.def 2024-05-08 L3 backend support: XeTeX
\g__graphics_track_int=\count266
\l__pdf_internal_box=\box52
\g__pdf_backend_annotation_int=\count267
\g__pdf_backend_link_int=\count268
))
Package: ctex 2022/07/14 v2.5.10 Chinese adapter in LaTeX (CTEX)

(e:/latex/texlive/2025/texmf-dist/tex/latex/ctex/ctexhook.sty
Package: ctexhook 2022/07/14 v2.5.10 Document and package hooks (CTEX)
)
(e:/latex/texlive/2025/texmf-dist/tex/latex/ctex/ctexpatch.sty
Package: ctexpatch 2022/07/14 v2.5.10 Patching commands (CTEX)
)
(e:/latex/texlive/2025/texmf-dist/tex/latex/base/fix-cm.sty
Package: fix-cm 2020/11/24 v1.1t fixes to LaTeX

(e:/latex/texlive/2025/texmf-dist/tex/latex/base/ts1enc.def
File: ts1enc.def 2001/06/05 v3.0e (jk/car/fm) Standard LaTeX file
LaTeX Font Info:    Redeclaring font encoding TS1 on input line 47.
))
\l__ctex_tmp_int=\count269
\l__ctex_tmp_box=\box53
\l__ctex_tmp_dim=\dimen142
\g__ctex_section_depth_int=\count270
\g__ctex_font_size_int=\count271

(e:/latex/texlive/2025/texmf-dist/tex/latex/ctex/config/ctexopts.cfg
File: ctexopts.cfg 2022/07/14 v2.5.10 Option configuration file (CTEX)
)
(e:/latex/texlive/2025/texmf-dist/tex/latex/ctex/engine/ctex-engine-xetex.def
File: ctex-engine-xetex.def 2022/07/14 v2.5.10 XeLaTeX adapter (CTEX)

(e:/latex/texlive/2025/texmf-dist/tex/xelatex/xecjk/xeCJK.sty
Package: xeCJK 2022/08/05 v3.9.1 Typesetting CJK scripts with XeLaTeX

(e:/latex/texlive/2025/texmf-dist/tex/latex/l3packages/xtemplate/xtemplate.sty
Package: xtemplate 2024-08-16 L3 Experimental prototype document functions
)
\l__xeCJK_tmp_int=\count272
\l__xeCJK_tmp_box=\box54
\l__xeCJK_tmp_dim=\dimen143
\l__xeCJK_tmp_skip=\skip51
\g__xeCJK_space_factor_int=\count273
\l__xeCJK_begin_int=\count274
\l__xeCJK_end_int=\count275
\c__xeCJK_CJK_class_int=\XeTeXcharclass1
\c__xeCJK_FullLeft_class_int=\XeTeXcharclass2
\c__xeCJK_FullRight_class_int=\XeTeXcharclass3
\c__xeCJK_HalfLeft_class_int=\XeTeXcharclass4
\c__xeCJK_HalfRight_class_int=\XeTeXcharclass5
\c__xeCJK_NormalSpace_class_int=\XeTeXcharclass6
\c__xeCJK_CM_class_int=\XeTeXcharclass7
\c__xeCJK_HangulJamo_class_int=\XeTeXcharclass8
\l__xeCJK_last_skip=\skip52
\c__xeCJK_none_node=\count276
\g__xeCJK_node_int=\count277
\c__xeCJK_CJK_node_dim=\dimen144
\c__xeCJK_CJK-space_node_dim=\dimen145
\c__xeCJK_default_node_dim=\dimen146
\c__xeCJK_CJK-widow_node_dim=\dimen147
\c__xeCJK_normalspace_node_dim=\dimen148
\c__xeCJK_default-space_node_skip=\skip53
\l__xeCJK_ccglue_skip=\skip54
\l__xeCJK_ecglue_skip=\skip55
\l__xeCJK_punct_kern_skip=\skip56
\l__xeCJK_indent_box=\box55
\l__xeCJK_last_penalty_int=\count278
\l__xeCJK_last_bound_dim=\dimen149
\l__xeCJK_last_kern_dim=\dimen150
\l__xeCJK_widow_penalty_int=\count279

LaTeX template Info: Declaring template type 'xeCJK/punctuation' taking 0
(template)           argument(s) on line 2396.

\l__xeCJK_fixed_punct_width_dim=\dimen151
\l__xeCJK_mixed_punct_width_dim=\dimen152
\l__xeCJK_middle_punct_width_dim=\dimen153
\l__xeCJK_fixed_margin_width_dim=\dimen154
\l__xeCJK_mixed_margin_width_dim=\dimen155
\l__xeCJK_middle_margin_width_dim=\dimen156
\l__xeCJK_bound_punct_width_dim=\dimen157
\l__xeCJK_bound_margin_width_dim=\dimen158
\l__xeCJK_margin_minimum_dim=\dimen159
\l__xeCJK_kerning_total_width_dim=\dimen160
\l__xeCJK_same_align_margin_dim=\dimen161
\l__xeCJK_different_align_margin_dim=\dimen162
\l__xeCJK_kerning_margin_width_dim=\dimen163
\l__xeCJK_kerning_margin_minimum_dim=\dimen164
\l__xeCJK_bound_dim=\dimen165
\l__xeCJK_reverse_bound_dim=\dimen166
\l__xeCJK_margin_dim=\dimen167
\l__xeCJK_minimum_bound_dim=\dimen168
\l__xeCJK_kerning_margin_dim=\dimen169
\g__xeCJK_family_int=\count280
\l__xeCJK_fam_int=\count281
\g__xeCJK_fam_allocation_int=\count282
\l__xeCJK_verb_case_int=\count283
\l__xeCJK_verb_exspace_skip=\skip57
(e:/latex/texlive/2025/texmf-dist/tex/latex/fontspec/fontspec.sty
(e:/latex/texlive/2025/texmf-dist/tex/latex/l3packages/xparse/xparse.sty
Package: xparse 2024-08-16 L3 Experimental document command parser
)
Package: fontspec 2024/05/11 v2.9e Font selection for XeLaTeX and LuaLaTeX

(e:/latex/texlive/2025/texmf-dist/tex/latex/fontspec/fontspec-xetex.sty
Package: fontspec-xetex 2024/05/11 v2.9e Font selection for XeLaTeX and LuaLaTe
X
\l__fontspec_script_int=\count284
\l__fontspec_language_int=\count285
\l__fontspec_strnum_int=\count286
\l__fontspec_tmp_int=\count287
\l__fontspec_tmpa_int=\count288
\l__fontspec_tmpb_int=\count289
\l__fontspec_tmpc_int=\count290
\l__fontspec_em_int=\count291
\l__fontspec_emdef_int=\count292
\l__fontspec_strong_int=\count293
\l__fontspec_strongdef_int=\count294
\l__fontspec_tmpa_dim=\dimen170
\l__fontspec_tmpb_dim=\dimen171
\l__fontspec_tmpc_dim=\dimen172

(e:/latex/texlive/2025/texmf-dist/tex/latex/base/fontenc.sty
Package: fontenc 2021/04/29 v2.0v Standard LaTeX package
)
(e:/latex/texlive/2025/texmf-dist/tex/latex/fontspec/fontspec.cfg)))
(e:/latex/texlive/2025/texmf-dist/tex/xelatex/xecjk/xeCJK.cfg
File: xeCJK.cfg 2022/08/05 v3.9.1 Configuration file for xeCJK package
))
\ccwd=\dimen173
\l__ctex_ccglue_skip=\skip58
)
\l__ctex_ziju_dim=\dimen174

(e:/latex/texlive/2025/texmf-dist/tex/latex/zhnumber/zhnumber.sty
Package: zhnumber 2022/07/14 v3.0 Typesetting numbers with Chinese glyphs
\l__zhnum_scale_int=\count295
\l__zhnum_tmp_int=\count296

(e:/latex/texlive/2025/texmf-dist/tex/latex/zhnumber/zhnumber-utf8.cfg
File: zhnumber-utf8.cfg 2022/07/14 v3.0 Chinese numerals with UTF8 encoding
))
(e:/latex/texlive/2025/texmf-dist/tex/latex/ctex/scheme/ctex-scheme-chinese.def
File: ctex-scheme-chinese.def 2022/07/14 v2.5.10 Chinese scheme for generic (CT
EX)
(e:/latex/texlive/2025/texmf-dist/tex/latex/ctex/config/ctex-name-utf8.cfg
File: ctex-name-utf8.cfg 2022/07/14 v2.5.10 Caption with encoding UTF-8 (CTEX)
))
(e:/latex/texlive/2025/texmf-dist/tex/latex/tools/indentfirst.sty
Package: indentfirst 2023/07/02 v1.03 Indent first paragraph (DPC)
)
(e:/latex/texlive/2025/texmf-dist/tex/latex/ctex/fontset/ctex-fontset-windows.d
ef
File: ctex-fontset-windows.def 2022/07/14 v2.5.10 Windows fonts definition (CTE
X)

Package fontspec Info: 
(fontspec)             Could not resolve font "KaiTi/B" (it probably doesn't
(fontspec)             exist).


Package fontspec Info: 
(fontspec)             Could not resolve font "SimHei/I" (it probably doesn't
(fontspec)             exist).


Package fontspec Info: 
(fontspec)             Could not resolve font "SimSun/BI" (it probably doesn't
(fontspec)             exist).


Package fontspec Info: 
(fontspec)             Font family 'SimSun(0)' created for font 'SimSun' with
(fontspec)             options
(fontspec)             [Script={CJK},BoldFont={SimHei},ItalicFont={KaiTi}].
(fontspec)              
(fontspec)              This font family consists of the following NFSS
(fontspec)             series/shapes:
(fontspec)              
(fontspec)             - 'normal' (m/n) with NFSS spec.:
(fontspec)             <->"SimSun/OT:script=hani;language=dflt;"
(fontspec)             - 'bold' (b/n) with NFSS spec.:
(fontspec)             <->"SimHei/OT:script=hani;language=dflt;"
(fontspec)             - 'italic' (m/it) with NFSS spec.:
(fontspec)             <->"KaiTi/OT:script=hani;language=dflt;"

)) (e:/latex/texlive/2025/texmf-dist/tex/latex/ctex/config/ctex.cfg
File: ctex.cfg 2022/07/14 v2.5.10 Configuration file (CTEX)
)
(e:/latex/texlive/2025/texmf-dist/tex/latex/amsmath/amsmath.sty
Package: amsmath 2024/11/05 v2.17t AMS math features
\@mathmargin=\skip59

For additional information on amsmath, use the `?' option.
(e:/latex/texlive/2025/texmf-dist/tex/latex/amsmath/amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text

(e:/latex/texlive/2025/texmf-dist/tex/latex/amsmath/amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks17
\ex@=\dimen175
))
(e:/latex/texlive/2025/texmf-dist/tex/latex/amsmath/amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen176
)
(e:/latex/texlive/2025/texmf-dist/tex/latex/amsmath/amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count297
LaTeX Info: Redefining \frac on input line 233.
\uproot@=\count298
\leftroot@=\count299
LaTeX Info: Redefining \overline on input line 398.
LaTeX Info: Redefining \colon on input line 409.
\classnum@=\count300
\DOTSCASE@=\count301
LaTeX Info: Redefining \ldots on input line 495.
LaTeX Info: Redefining \dots on input line 498.
LaTeX Info: Redefining \cdots on input line 619.
\Mathstrutbox@=\box56
\strutbox@=\box57
LaTeX Info: Redefining \big on input line 721.
LaTeX Info: Redefining \Big on input line 722.
LaTeX Info: Redefining \bigg on input line 723.
LaTeX Info: Redefining \Bigg on input line 724.
\big@size=\dimen177
LaTeX Font Info:    Redeclaring font encoding OML on input line 742.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 743.
\macc@depth=\count302
LaTeX Info: Redefining \bmod on input line 904.
LaTeX Info: Redefining \pmod on input line 909.
LaTeX Info: Redefining \smash on input line 939.
LaTeX Info: Redefining \relbar on input line 969.
LaTeX Info: Redefining \Relbar on input line 970.
\c@MaxMatrixCols=\count303
\dotsspace@=\muskip17
\c@parentequation=\count304
\dspbrk@lvl=\count305
\tag@help=\toks18
\row@=\count306
\column@=\count307
\maxfields@=\count308
\andhelp@=\toks19
\eqnshift@=\dimen178
\alignsep@=\dimen179
\tagshift@=\dimen180
\tagwidth@=\dimen181
\totwidth@=\dimen182
\lineht@=\dimen183
\@envbody=\toks20
\multlinegap=\skip60
\multlinetaggap=\skip61
\mathdisplay@stack=\toks21
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
)
(e:/latex/texlive/2025/texmf-dist/tex/latex/amsfonts/amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
)
(e:/latex/texlive/2025/texmf-dist/tex/latex/amsfonts/amssymb.sty
Package: amssymb 2013/01/14 v3.01 AMS font symbols
)
(e:/latex/texlive/2025/texmf-dist/tex/latex/algorithms/algorithm.sty
Invalid UTF-8 byte or sequence at line 11 replaced by U+FFFD.
Package: algorithm 2009/08/24 v0.1 Document Style `algorithm' - floating enviro
nment

(e:/latex/texlive/2025/texmf-dist/tex/latex/float/float.sty
Package: float 2001/11/08 v1.3d Float enhancements (AL)
\c@float@type=\count309
\float@exts=\toks22
\float@box=\box58
\@float@everytoks=\toks23
\@floatcapt=\box59
)
(e:/latex/texlive/2025/texmf-dist/tex/latex/base/ifthen.sty
Package: ifthen 2024/03/16 v1.1e Standard LaTeX ifthen package (DPC)
)
\@float@every@algorithm=\toks24
\c@algorithm=\count310
)
(e:/latex/texlive/2025/texmf-dist/tex/latex/algorithms/algorithmic.sty
Invalid UTF-8 byte or sequence at line 11 replaced by U+FFFD.
Package: algorithmic 2009/08/24 v0.1 Document Style `algorithmic'

(e:/latex/texlive/2025/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks25
)
\c@ALC@unique=\count311
\c@ALC@line=\count312
\c@ALC@rem=\count313
\c@ALC@depth=\count314
\ALC@tlm=\skip62
\algorithmicindent=\skip63
)
(e:/latex/texlive/2025/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)

(e:/latex/texlive/2025/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2024/08/06 v1.4g Standard LaTeX Graphics (DPC,SPQR)

(e:/latex/texlive/2025/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2023/12/02 v1.11 sin cos tan (DPC)
)
(e:/latex/texlive/2025/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: xetex.def on input line 106.

(e:/latex/texlive/2025/texmf-dist/tex/latex/graphics-def/xetex.def
File: xetex.def 2022/09/22 v5.0n Graphics/color driver for xetex
))
\Gin@req@height=\dimen184
\Gin@req@width=\dimen185
)
(e:/latex/texlive/2025/texmf-dist/tex/latex/geometry/geometry.sty
Package: geometry 2020/01/02 v5.9 Page Geometry

(e:/latex/texlive/2025/texmf-dist/tex/generic/iftex/ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.

(e:/latex/texlive/2025/texmf-dist/tex/generic/iftex/iftex.sty
Package: iftex 2024/12/12 v1.0g TeX engine tests
))
\Gm@cnth=\count315
\Gm@cntv=\count316
\c@Gm@tempcnt=\count317
\Gm@bindingoffset=\dimen186
\Gm@wd@mp=\dimen187
\Gm@odd@mp=\dimen188
\Gm@even@mp=\dimen189
\Gm@layoutwidth=\dimen190
\Gm@layoutheight=\dimen191
\Gm@layouthoffset=\dimen192
\Gm@layoutvoffset=\dimen193
\Gm@dimlist=\toks26
)
(e:/latex/texlive/2025/texmf-dist/tex/latex/booktabs/booktabs.sty
Package: booktabs 2020/01/12 v1.61803398 Publication quality tables
\heavyrulewidth=\dimen194
\lightrulewidth=\dimen195
\cmidrulewidth=\dimen196
\belowrulesep=\dimen197
\belowbottomsep=\dimen198
\aboverulesep=\dimen199
\abovetopsep=\dimen256
\cmidrulesep=\dimen257
\cmidrulekern=\dimen258
\defaultaddspace=\dimen259
\@cmidla=\count318
\@cmidlb=\count319
\@aboverulesep=\dimen260
\@belowrulesep=\dimen261
\@thisruleclass=\count320
\@lastruleclass=\count321
\@thisrulewidth=\dimen262
)
(e:/latex/texlive/2025/texmf-dist/tex/latex/multirow/multirow.sty
Package: multirow 2024/11/12 v2.9 Span multiple rows of a table
\multirow@colwidth=\skip64
\multirow@cntb=\count322
\multirow@dima=\skip65
\bigstrutjot=\dimen263
)
(e:/latex/texlive/2025/texmf-dist/tex/latex/tools/array.sty
Package: array 2024/10/17 v2.6g Tabular extension package (FMi)
\col@sep=\dimen264
\ar@mcellbox=\box60
\extrarowheight=\dimen265
\NC@list=\toks27
\extratabsurround=\skip66
\backup@length=\skip67
\ar@cellbox=\box61
)
No file 第三部分_所提算法.aux.
\openout1 = `第三部分_所提算法.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 21.
LaTeX Font Info:    ... okay on input line 21.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 21.
LaTeX Font Info:    ... okay on input line 21.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 21.
LaTeX Font Info:    ... okay on input line 21.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 21.
LaTeX Font Info:    ... okay on input line 21.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 21.
LaTeX Font Info:    ... okay on input line 21.
LaTeX Font Info:    Checking defaults for TU/lmr/m/n on input line 21.
LaTeX Font Info:    ... okay on input line 21.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 21.
LaTeX Font Info:    ... okay on input line 21.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 21.
LaTeX Font Info:    ... okay on input line 21.

Package fontspec Info: 
(fontspec)             Adjusting the maths setup (use [no-math] to avoid
(fontspec)             this).

\symlegacymaths=\mathgroup6
LaTeX Font Info:    Overwriting symbol font `legacymaths' in version `bold'
(Font)                  OT1/cmr/m/n --> OT1/cmr/bx/n on input line 21.
LaTeX Font Info:    Redeclaring math accent \acute on input line 21.
LaTeX Font Info:    Redeclaring math accent \grave on input line 21.
LaTeX Font Info:    Redeclaring math accent \ddot on input line 21.
LaTeX Font Info:    Redeclaring math accent \tilde on input line 21.
LaTeX Font Info:    Redeclaring math accent \bar on input line 21.
LaTeX Font Info:    Redeclaring math accent \breve on input line 21.
LaTeX Font Info:    Redeclaring math accent \check on input line 21.
LaTeX Font Info:    Redeclaring math accent \hat on input line 21.
LaTeX Font Info:    Redeclaring math accent \dot on input line 21.
LaTeX Font Info:    Redeclaring math accent \mathring on input line 21.
LaTeX Font Info:    Redeclaring math symbol \Gamma on input line 21.
LaTeX Font Info:    Redeclaring math symbol \Delta on input line 21.
LaTeX Font Info:    Redeclaring math symbol \Theta on input line 21.
LaTeX Font Info:    Redeclaring math symbol \Lambda on input line 21.
LaTeX Font Info:    Redeclaring math symbol \Xi on input line 21.
LaTeX Font Info:    Redeclaring math symbol \Pi on input line 21.
LaTeX Font Info:    Redeclaring math symbol \Sigma on input line 21.
LaTeX Font Info:    Redeclaring math symbol \Upsilon on input line 21.
LaTeX Font Info:    Redeclaring math symbol \Phi on input line 21.
LaTeX Font Info:    Redeclaring math symbol \Psi on input line 21.
LaTeX Font Info:    Redeclaring math symbol \Omega on input line 21.
LaTeX Font Info:    Redeclaring math symbol \mathdollar on input line 21.
LaTeX Font Info:    Redeclaring symbol font `operators' on input line 21.
LaTeX Font Info:    Encoding `OT1' has changed to `TU' for symbol font
(Font)              `operators' in the math version `normal' on input line 21.
LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  OT1/cmr/m/n --> TU/lmr/m/n on input line 21.
LaTeX Font Info:    Encoding `OT1' has changed to `TU' for symbol font
(Font)              `operators' in the math version `bold' on input line 21.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  OT1/cmr/bx/n --> TU/lmr/m/n on input line 21.
LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  TU/lmr/m/n --> TU/lmr/m/n on input line 21.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `normal'
(Font)                  OT1/cmr/m/it --> TU/lmr/m/it on input line 21.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `normal'
(Font)                  OT1/cmr/bx/n --> TU/lmr/b/n on input line 21.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `normal'
(Font)                  OT1/cmss/m/n --> TU/lmss/m/n on input line 21.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `normal'
(Font)                  OT1/cmtt/m/n --> TU/lmtt/m/n on input line 21.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  TU/lmr/m/n --> TU/lmr/b/n on input line 21.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `bold'
(Font)                  OT1/cmr/bx/it --> TU/lmr/b/it on input line 21.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `bold'
(Font)                  OT1/cmss/bx/n --> TU/lmss/b/n on input line 21.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `bold'
(Font)                  OT1/cmtt/m/n --> TU/lmtt/b/n on input line 21.
*geometry* driver: auto-detecting
*geometry* detected driver: xetex
*geometry* verbose mode - [ preamble ] result:
* driver: xetex
* paper: a4paper
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: 
* h-part:(L,W,R)=(71.13188pt, 455.24411pt, 71.13188pt)
* v-part:(T,H,B)=(71.13188pt, 702.78308pt, 71.13188pt)
* \paperwidth=597.50787pt
* \paperheight=845.04684pt
* \textwidth=455.24411pt
* \textheight=702.78308pt
* \oddsidemargin=-1.1381pt
* \evensidemargin=-1.1381pt
* \topmargin=-38.1381pt
* \headheight=12.0pt
* \headsep=25.0pt
* \topskip=12.0pt
* \footskip=30.0pt
* \marginparwidth=44.0pt
* \marginparsep=10.0pt
* \columnsep=10.0pt
* \skip\footins=10.8pt plus 4.0pt minus 2.0pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumnfalse
* \@twosidefalse
* \@mparswitchfalse
* \@reversemarginfalse
* (1in=72.27pt=25.4mm, 1cm=28.453pt)

LaTeX Font Info:    Trying to load font information for U+msa on input line 23.

(e:/latex/texlive/2025/texmf-dist/tex/latex/amsfonts/umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 23.


(e:/latex/texlive/2025/texmf-dist/tex/latex/amsfonts/umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
)

[1

]

[2]

[3]

[4]

[5]

[6]

[7]

[8]

[9]

[10]
Overfull \vbox (234.07756pt too high) has occurred while \output is active []



[11]

[12]

[13]

[14]

[15] (./第三部分_所提算法.aux)
 ***********
LaTeX2e <2024-11-01> patch level 2
L3 programming layer <2022/07/14>
 ***********


LaTeX Warning: Label(s) may have changed. Rerun to get cross-references right.

 ) 
Here is how much of TeX's memory you used:
 8781 strings out of 473832
 227044 string characters out of 5730405
 640186 words of memory out of 5000000
 31714 multiletter control sequences out of 15000+600000
 567963 words of font info for 97 fonts, out of 8000000 for 9000
 1348 hyphenation exceptions out of 8191
 93i,10n,111p,368b,648s stack positions out of 10000i,1000n,20000p,200000b,200000s

Output written on 第三部分_所提算法.pdf (15 pages).
