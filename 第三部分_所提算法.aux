\relax 
\@writefile{toc}{\contentsline {section}{\numberline {1}所提算法}{1}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {1.1}算法总体框架}{1}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {1.2}问题建模与编码方案}{1}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {1.2.1}问题数学建模}{1}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {1.2.2}编码方案}{2}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {1.3}混合初始化策略}{2}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {1.3.1}基于LHS的空间采样}{2}{}\protected@file@percent }
\@writefile{loa}{\contentsline {algorithm}{\numberline {1}{\ignorespaces 基于直径的网络区域划分}}{2}{}\protected@file@percent }
\@writefile{loa}{\contentsline {algorithm}{\numberline {2}{\ignorespaces 基于LHS的初始解生成}}{3}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {1.3.2}基于度中心性的启发式采样}{3}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {1.3.3}质量与多样性筛选}{3}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {1.4}地形状态感知机制}{4}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {1.4.1}地形状态值计算}{4}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {1.4.2}动态阈值计算}{4}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {1.4.3}搜索状态划分}{4}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {1.5}状态驱动的算子调度机制}{5}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {1.5.1}变异算子设计}{5}{}\protected@file@percent }
\@writefile{toc}{\contentsline {paragraph}{收敛状态变异算子（DE/best/1）}{5}{}\protected@file@percent }
\@writefile{loa}{\contentsline {algorithm}{\numberline {3}{\ignorespaces 收敛状态变异算子}}{5}{}\protected@file@percent }
\@writefile{toc}{\contentsline {paragraph}{开发状态变异算子（DE/current-to-best/1）}{5}{}\protected@file@percent }
\@writefile{loa}{\contentsline {algorithm}{\numberline {4}{\ignorespaces 开发状态变异算子}}{6}{}\protected@file@percent }
\@writefile{toc}{\contentsline {paragraph}{探索状态变异算子（DE/rand/1）}{6}{}\protected@file@percent }
\@writefile{loa}{\contentsline {algorithm}{\numberline {5}{\ignorespaces 探索状态变异算子}}{6}{}\protected@file@percent }
\@writefile{toc}{\contentsline {paragraph}{逃逸状态优化算子}{6}{}\protected@file@percent }
\@writefile{loa}{\contentsline {algorithm}{\numberline {6}{\ignorespaces 逃逸状态优化算子}}{6}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {1.5.2}交叉算子}{7}{}\protected@file@percent }
\@writefile{loa}{\contentsline {algorithm}{\numberline {7}{\ignorespaces 均匀交叉算子}}{7}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {1.6}参数自适应机制}{7}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {1.6.1}参数生成}{7}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {1.6.2}参数更新}{7}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {1.7}适应度评估}{8}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {1.7.1}LIE函数详细计算}{8}{}\protected@file@percent }
\@writefile{toc}{\contentsline {paragraph}{种子节点贡献}{8}{}\protected@file@percent }
\@writefile{toc}{\contentsline {paragraph}{一跳邻居贡献}{8}{}\protected@file@percent }
\@writefile{toc}{\contentsline {paragraph}{二跳邻居贡献}{8}{}\protected@file@percent }
\@writefile{toc}{\contentsline {paragraph}{LIE函数总公式}{8}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {1.7.2}适应度缓存机制}{9}{}\protected@file@percent }
\@writefile{loa}{\contentsline {algorithm}{\numberline {8}{\ignorespaces 适应度缓存机制}}{9}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {1.8}算法实现优化策略}{9}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {1.8.1}并行化策略}{9}{}\protected@file@percent }
\@writefile{toc}{\contentsline {paragraph}{适应度评估并行化}{9}{}\protected@file@percent }
\@writefile{loa}{\contentsline {algorithm}{\numberline {9}{\ignorespaces 并行适应度评估}}{9}{}\protected@file@percent }
\@writefile{toc}{\contentsline {paragraph}{距离矩阵计算向量化}{10}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {1.8.2}内存优化}{10}{}\protected@file@percent }
\@writefile{toc}{\contentsline {paragraph}{节点ID连续化}{10}{}\protected@file@percent }
\@writefile{loa}{\contentsline {algorithm}{\numberline {10}{\ignorespaces 节点ID连续化}}{10}{}\protected@file@percent }
\@writefile{toc}{\contentsline {paragraph}{预计算网络特征}{10}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {1.9}LADE算法完整流程}{11}{}\protected@file@percent }
\@writefile{loa}{\contentsline {algorithm}{\numberline {11}{\ignorespaces LADE算法主流程}}{11}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {1.10}算法复杂度分析}{12}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {1.10.1}时间复杂度}{12}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {1.10.2}空间复杂度}{12}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {1.11}算法特性分析}{13}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {1.11.1}收敛性分析}{13}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {1.11.2}参数敏感性}{13}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {1.12}与现有方法的区别}{13}{}\protected@file@percent }
\@writefile{lot}{\contentsline {table}{\numberline {1}{\ignorespaces LADE与现有方法的比较}}{13}{}\protected@file@percent }
\newlabel{tab:comparison}{{1}{13}{}{table.1}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {1.13}算法关键技术总结}{14}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {1.13.1}地形状态建模技术}{14}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {1.13.2}状态驱动的搜索策略}{14}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {1.13.3}参数自适应控制}{14}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {1.14}算法创新性分析}{14}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {1.15}本章小结}{15}{}\protected@file@percent }
\gdef \@abspage@last{15}
