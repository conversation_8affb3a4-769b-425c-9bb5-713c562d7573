# LADE算法完整流程详细伪代码

## 算法主流程

```pseudocode
Algorithm: LADE (Landscape-Aware Adaptive Differential Evolution)
Input: 
    - G = (V, E): 社交网络图
    - k: 种子节点数量
    - NP: 种群大小
    - FEs_max: 最大函数评估次数
    - G_max: 最大迭代次数
    - p: 激活概率
    - SN: 采样数量
Output: 
    - S*: 最优种子集合

// ========================================
// 阶段1：预处理
// ========================================
1.  bridge_nodes ← detect_bridge_nodes(G)
    // 使用介数中心性检测桥节点
    
2.  combined_scores ← calculate_combined_centrality_igraph(G)
    // 计算综合中心性评分（度中心性 + 介数中心性）
    
3.  G ← convert_node_labels_to_integers(G)
    // 将节点ID映射为连续整数 {0, 1, 2, ..., n-1}
    
4.  预计算LFV值缓存
    for each node v in V:
        LFV_cache[v] ← compute_LFV(v, G, p)
        // LFV(v) = 1 + Σ_{u∈N(v)} (p + p · Σ_{w∈N(u)\{v}} p)

// ========================================
// 阶段2：混合初始化
// ========================================
5.  regions ← divide_by_diameter(G)
    // 基于网络直径路径划分区域
    
6.  S_LHS ← sample_lhs(G, k, SN/2, bridge_nodes, combined_scores)
    // 拉丁超立方采样，生成 SN/2 个解
    
7.  S_score ← sample_score(G, k, SN/2)
    // 基于度中心性采样，生成 SN/2 个解
    
8.  all_solutions ← S_LHS ∪ S_score
    // 合并所有候选解
    
9.  fitness_cache ← parallel_compute_fitness(all_solutions, G, p)
    // 并行计算所有候选解的LIE适应度值
    
10. high_quality ← quality_filter(all_solutions, fitness_cache, 0.7 * |all_solutions|)
    // 质量筛选：选择前70%的高质量解
    
11. diverse_solutions ← diversity_filter(high_quality, similarity_threshold=0.8)
    // 多样性筛选：过滤相似度超过0.8的解
    
12. P^(0) ← initialize_population_hybrid(diverse_solutions, NP)
    // 从筛选后的解中选择NP个个体作为初始种群

// ========================================
// 阶段3：参数初始化
// ========================================
13. μ_CR ← 0.5, μ_F ← 0.5, c ← 0.1
    // 初始化参数分布均值和学习率
    
14. Λ_history ← ∅, g ← 0, FEs ← 0
    // 初始化历史记录和计数器
    
15. lambda^(0) ← compute_lambda(P^(0))
    // 计算初始地形状态值
    
16. Λ_history ← Λ_history ∪ {lambda^(0)}
    // 记录初始λ值
    
17. stagnation_counter ← 0
    // 初始化停滞计数器

// ========================================
// 阶段4：主进化循环
// ========================================
18. while FEs < FEs_max AND g < G_max:
19.     g ← g + 1
    
    // ---- 地形状态分析 ----
20.     lambda^(g) ← compute_lambda(P^(g-1))
        // 计算当前地形状态值
        
21.     Λ_history ← Λ_history ∪ {lambda^(g)}
        // 更新历史记录
        
22.     current_state ← determine_state(lambda^(g), Λ_history, g)
        // 确定当前搜索状态：convergence/exploitation/exploration/escape
        
23.     if current_state == 'escape':
24.         escape_candidates ← generate_escape_candidates(P^(g-1))
        // 生成逃逸候选池
    
    // ---- 种群进化 ----
25.     S_CR ← ∅, S_F ← ∅
        // 初始化成功参数集合
        
26.     new_population ← ∅
    
27.     for i = 1 to NP:
28.         X_i ← P^(g-1)[i]  // 当前个体
        
        // 参数生成
29.         CR_i ← Normal(μ_CR, 0.1²)  // 截断到[0,1]
30.         F_i ← Cauchy(μ_F, 0.1)     // 截断到[0,1]
        
        // 变异操作
31.         switch current_state:
32.             case 'convergence':
33.                 V_i ← convergence_mutation(X_i, F_i)  // DE/best/1
34.             case 'exploitation':
35.                 V_i ← exploitation_mutation(X_i, F_i)  // DE/current-to-best/1
36.             case 'exploration':
37.                 V_i ← exploration_mutation(X_i, F_i)   // DE/rand/1
38.             case 'escape':
39.                 V_i ← escape_mutation(X_i, escape_candidates)
        
        // 交叉操作
40.         U_i ← binomial_crossover(X_i, V_i, CR_i, k)
        
        // 选择操作
41.         fitness_trial ← LIE(U_i)
42.         fitness_target ← LIE(X_i)
43.         FEs ← FEs + 1  // 更新函数评估计数
        
44.         if fitness_trial >= fitness_target:
45.             X_i^(g) ← U_i
46.             S_CR ← S_CR ∪ {CR_i}  // 记录成功参数
47.             S_F ← S_F ∪ {F_i}
48.         else:
49.             X_i^(g) ← X_i
        
50.         new_population ← new_population ∪ {X_i^(g)}
    
    // ---- 局部搜索 ----
51.     num_to_optimize ← max(1, ⌊0.1 × NP⌋)
        // 选择前10%的个体进行局部搜索
        
52.     candidates ← top_individuals(new_population, num_to_optimize)
        // 按适应度排序选择候选个体
        
53.     for each candidate in candidates:
54.         optimized ← local_search_LFV(candidate, max_neighbors=8)
55.         if LIE(optimized) > LIE(candidate):
56.             更新new_population中对应个体为optimized
57.             FEs ← FEs + 局部搜索中的函数评估次数
    
    // ---- 全局最优局部搜索 ----
58.     G_best ← argmax_{X ∈ new_population} LIE(X)
        // 找到当前最优个体
        
59.     G_best_optimized ← local_search_LFV(G_best, max_neighbors=10)
        // 对全局最优进行深度局部搜索
        
60.     if LIE(G_best_optimized) > LIE(G_best):
61.         更新new_population中的G_best为G_best_optimized
62.         FEs ← FEs + 局部搜索中的函数评估次数
    
    // ---- 参数更新 ----
63.     if S_CR ≠ ∅ AND S_F ≠ ∅:
64.         μ_CR ← (1-c) · μ_CR + c · mean(S_CR)
65.         μ_F ← (1-c) · μ_F + c · mean(S_F)
    
    // ---- 停滞检测 ----
66.     current_best_fitness ← max(LIE(X) for X in new_population)
67.     if current_best_fitness没有改进:
68.         stagnation_counter ← stagnation_counter + 1
69.     else:
70.         stagnation_counter ← 0
    
71.     P^(g) ← new_population
        // 更新种群

// ========================================
// 阶段5：返回结果
// ========================================
72. S* ← argmax_{X ∈ P^(g)} LIE(X)
    // 返回最优解

73. return S*
```

## 关键子算法详细说明

### 1. 地形状态值计算
```pseudocode
Function: compute_lambda(population)
Input: population P = {X_1, X_2, ..., X_NP}
Output: lambda value

1. if |P| < 2:
2.     return 0.5
3. 
4. // 批量计算适应度
5. fitness_values ← [LIE(X_i) for X_i in P]
6. best_idx ← argmax(fitness_values)
7. 
8. // 计算加权PDI距离矩阵
9. distance_matrix ← zeros(NP, NP)
10. for i = 1 to NP:
11.     for j = i+1 to NP:
12.         symmetric_diff ← P[i] △ P[j]
13.         union_set ← P[i] ∪ P[j]
14.         numerator ← Σ_{v ∈ symmetric_diff} LFV_cache[v]
15.         denominator ← Σ_{v ∈ union_set} LFV_cache[v]
16.         distance_matrix[i][j] ← numerator / denominator
17.         distance_matrix[j][i] ← distance_matrix[i][j]
18. 
19. // 计算平均距离
20. avg_distances ← [mean(distance_matrix[i]) for i in range(NP)]
21. 
22. d_g ← avg_distances[best_idx]
23. d_max ← max(avg_distances)
24. d_min ← min(avg_distances)
25. 
26. if d_max == d_min:
27.     return 0.5
28. 
29. lambda ← (d_g - d_min) / (d_max - d_min)
30. return lambda
```

### 2. 状态判断
```pseudocode
Function: determine_state(lambda_val, lambda_history, generation)
Input: lambda_val, lambda_history, current generation
Output: search state

1. // 前10代强制探索
2. if generation < 10:
3.     return 'exploration'
4. 
5. // 检查逃逸条件
6. if lambda_val < 0.1 AND stagnation_counter >= 5:
7.     return 'escape'
8. 
9. // 计算动态阈值
10. if len(lambda_history) >= 4:
11.     Q1 ← percentile(lambda_history, 25)
12.     Q3 ← percentile(lambda_history, 75)
13. else:
14.     Q1 ← 0.25, Q3 ← 0.75  // 默认阈值
15. 
16. // 状态划分
17. if lambda_val in [0.0, Q1):
18.     return 'convergence'
19. elif lambda_val in [Q1, (Q1+Q3)/2):
20.     return 'exploitation'
21. elif lambda_val in [(Q1+Q3)/2, Q3):
22.     return 'exploration'
23. else:  // lambda_val in [Q3, 1.0]
24.     return 'escape'
```

### 3. 局部搜索详细实现
```pseudocode
Function: local_search_LFV(individual, max_neighbors)
Input: individual X, max_neighbors
Output: optimized individual X'

1. X' ← copy(X)
2. best_fitness ← LIE(X')
3. improvement_found ← false
4. 
5. // 按LFV值升序排序（优先替换低影响力节点）
6. sorted_nodes ← sort(X', key=lambda v: LFV_cache[v])
7. 
8. for each node in sorted_nodes:
9.     // 获取该节点的top-k邻居
10.     neighbors ← get_top_neighbors(node, max_neighbors)
11.     
12.     for each neighbor in neighbors:
13.         if neighbor in X':
14.             continue  // 跳过已在解中的节点
15.         
16.         // 生成新解
17.         X_new ← copy(X')
18.         X_new[X'.index(node)] ← neighbor
19.         
20.         // 评估新解
21.         new_fitness ← LIE(X_new)
22.         
23.         if new_fitness > best_fitness:
24.             X' ← X_new
25.             best_fitness ← new_fitness
26.             improvement_found ← true
27.             break  // 找到改进即跳出内层循环
28.     
29.     if improvement_found:
30.         break  // 找到改进即跳出外层循环
31. 
32. return X'
```

## 算法特点总结

1. **四阶段结构**：预处理 → 初始化 → 主循环 → 结果返回
2. **地形状态驱动**：基于λ值实时调整搜索策略
3. **混合初始化**：LHS采样 + 度中心性采样
4. **自适应参数**：CR和F参数的动态更新
5. **局部搜索集成**：种群优化 + 全局最优优化
6. **并行优化**：适应度评估和距离计算的并行化
7. **缓存机制**：LFV值和适应度的智能缓存
