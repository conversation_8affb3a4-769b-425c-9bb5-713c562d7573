感知地形状态的多阶段差分进化方法在社交网络影响力最大化中的应用
A Multi-Stage Landscape-Aware Differential Evolution Approach for Social Network Influence Maximization
基于地形状态驱动的自适应差分进化影响力最大化算法
Adaptive Differential Evolution for Influence Maximization via Landscape State-Driven Control
1.引言
社交网络是社会成员之间通过各种社会关系结成的网络体系，在信息传播、舆情演化与精准营销等场景中发挥着日益突出的作用。随着微博、抖音、Twitter等平台的快速兴起（迅猛发展），网络中用户关系与行为交互构成了高度复杂的信息流系统。在此背景下，影响力最大化（Influence Maximization, IM）问题成为社交网络分析的核心之一，其目标是选择最小规模的种子节点集合，在特定扩散模型下最大化预期传播范围。该问题兼具理论挑战性与应用价值，已引发多学科持续关注。
自Domingos和Richardson首次提出将IM问题形式化为算法任务以来，Kempe等人基于信息扩散模型对该问题进行建模，首次在独立级联（IC）与线性阈值（LT）模型下将其形式化为组合优化问题，并严格证明其 NP-hard 特性。这一工作奠定了IM问题的理论基础,并在此基础上提出了基于子模性原理的贪心算法，并证明其在单调子模函数下可达到近似比1−1/e的最优近似比保证。尽管贪心算法具备较强的理论支撑，但其计算效率严重依赖于大规模蒙特卡洛模拟，难以适用于大规模网络。后续改进（如 CELF[5], CELF++[6]）虽部分缓解了开销，但仍未从根本上摆脱对高成本模拟的依赖。为了克服该类方法的局限性，一系列结构启发式方法（如 DC+[7]、融合引力模型 FGM[8]、HCCKshell[9]）被提出，这些方法通过融合节点度、k-shell、特征向量中心性、引力模型等局部/半全局特征快速评估节点影响力。然而，这类方法忽略了扩散动态过程，准确性高度依赖网络结构，缺乏全局最优性保障。为兼顾搜索效率与解的质量，研究者逐渐将元启发式算法引入IM问题的求解过程中。此类算法具备全局搜索能力与梯度非依赖性，在处理此类复杂优化问题中展现出天然优势（如粒子群优化[10]、差分进化[11]）。然而，IM问题的解空间呈现强非凸性、多峰性及传播响应的高度非线性特征[12]，使得种群演化过程易陷于局部结构盆地，难以实现有效跃迁。元启发式算法由于受制于搜索行为的盲态性与状态不可感知性，在求解IM问题时面临固有挑战：其一，缺乏对搜索行为与空间结构关系的动态建模，难以实现基于种群演化状态的策略调节；其二，缺乏状态反馈机制，难以识别并跳出局部最优陷阱，影响搜索的全局性与稳定性。归根结底，结构感知能力的缺失成为制约元启发式算法适应复杂搜索空间的关键瓶颈。
适应度地形理论为分析优化问题的解空间结构及搜索行为的动态特征提供了有力工具，有助于深入理解种群演化机制[13]。已有研究表明，将适应度地形分析引入元启发式算法，有望有效克服其搜索行为盲目性，提升算法性能[14]。因此，利用适应度地形理论指导元启发式算法设计，已逐步发展为应对复杂优化问题的一种有效方法论范式。
本文引入适应度地形分析，构建结构-行为映射机制，以提升算法对搜索状态的识别和自适应调节能力。具体地，提出了基于地形状态感知的差分进化算法，面向IM问题设计多维地形状态指标，其核心通过一个地形状态值来体现，该值动态刻画搜索行为与解空间结构属性之间的耦合关系。并据此设计状态驱动的算子调度机制，实现不同演化阶段的搜索策略自适应切换与跳跃，从而提升算法的全局搜索能力和收敛稳定性。
本文的主要贡献总结如下：
构建地形状态建模机制：融合个体分布与传播行为特征的多维状态指标，定义地形状态值λ，实现搜索行为与解空间结构的动态耦合建模。
设计状态自适应的算子调度机制：基于λ值划分收敛/开发/探索/逃逸四态，设计变异算子与搜索状态的自适应匹配策略。引入逃逸机制跳出局部最优盆地，显著提升全局搜索能力。
本文结构安排如下：
第 2 节综述了影响力最大化问题与适应度地形分析的相关研究工作；
第 3 节介绍了所提出算法所需的基础知识；
第 4 节详细描述了LADE的具体实现方法；
第 5 节展示了实验结果与分析；
第 6 节从算法机制角度讨论LADE与现有方法的区别及潜在不足；
第 7 节对全文进行总结并展望未来研究方向。
2.相关工作
2.1 基于近似的方法
影响力最大化问题的研究始于Kempe等人的开创性工作[4]，其提出的经典的贪心算法通过迭代选择边际增益最大的节点，首次在理论上获得了(1-1/e)的最优近似比。然而，该算法的计算效率存在明显瓶颈：单次节点边际增益评估需上万次扩散模拟，以及迭代过程中的边际增益重复计算导致大量冗余开销。为此，CELF[5]、CELF++[6]等通过惰性更新减少冗余，但在大规模网络下仍受高昂模拟开销影响。为应对这一问题，基于反向可达集（RR set）的采样框架被提出[22],代表性方法如IMM[15]、TIM/TIM+[16]和D-SSA[17]等，将影响力估算转化为集合覆盖，大幅加快算法速度。但随着网络规模或精度要求提升，RR集采样与存储资源消耗显著增加，成为新的效率瓶颈。
为缓解该问题，Guo等人提出通过子集采样和两阶段设计的HIST算法，降低了内存消耗[11]；Shahrouz et al.提出gIM等并行化方法，利用GPU显著加速采样[7]。此外，Rui等人提出了一种兼顾公平性与可扩展性的近似算法框架[NEW]，通过设计无偏估计器，有效控制采样规模，实现了大规模网络上的公平影响力最大化。
总体来看，近似方法在准确性和理论保证方面具有突出优势，但在大规模网络下，计算与存储资源消耗依然是亟待解决的难题。
2.2 启发式方法
相比于基于近似的方法的理论保证，启发式方法凭借实现简便和高效计算，广泛应用于大规模网络的影响力最大化任务。Shen等人[1]提出的局部度量维数（LDD）方法，结合节点不同层次邻居数量变化，提高了关键节点识别的准确性。Wang等人[2]设计融合多结构信息的向心中心性及排除策略，减少种子冗余。Chen等人[3]以度与平均邻居度复合指标（DC+）及其重力模型扩展，增强了同度节点的区分能力与传播效果。Guo等人[4]提出融合重力模型（FGM），综合节点度、K-shell与特征向量等多属性特征，实现节点局部与全局影响力的融合评估，在多类网络下具备更强适应性。Yang等人[5]基于社区结构，提出“峰-坡-谷”拓扑势结构的递归聚类方法（CIM），实现多社区内种子分散布局，显著提升了传播覆盖与算法稳定性。
尽管启发式方法高效且实用，但受限于网络结构的复杂性，其整体性能和解的质量仍存在提升空间，因此亟需探索更优的新方法。
2.3 基于机器学习的方法
近年来，机器学习，尤其是深度学习和图神经网络（GNN）技术，为影响力最大化（IM）问题带来了全新思路。此类模型依托自动特征提取和分层结构建模，能够融合复杂网络属性，显著提升节点影响力的刻画能力。Chen 等人提出的 ToupleGDD 框架将深度强化学习和图神经网络应用于影响力最大化任务，从而实现了高效的影响力建模，并在不同网络之间表现出较强的泛化能力。Li等人提出的HCCKshell方法结合预训练语言模型与图卷积网络，通过加权融合异质内容与结构特征及熵度量，提高节点表征、多样性和传播覆盖。Tang等人的GCNT模型将GCN与图Transformer深度整合，利用多维中心性和动态标签机制增强网络关系建模。Zhu等人开发的BiGDN框架以GNN与深度强化学习为核心，结合知识蒸馏和预训练，在大规模网络下兼具推理速度与预测精度。
现有方法受限于传播路径重叠和标签设计不足，训练标签区分度与质量较低。在大规模网络下，模型训练阶段频繁的节点表征与影响力计算导致显著的计算复杂度和资源消耗。
2.4 基于元启发式的方法（8篇）
元启发式算法凭借其全局搜索能力和良好的适应性，成为突破传统启发式方法在影响力最大化问题上局限的有力工具。以Gong等人[1]首次提出的离散粒子群优化（DPSO）为代表，其将 IM 问题重构为优化局部影响力评估函数（LIE），并设计了离散化的位置和速度更新规则，有效提升了传播估计精度，但高复杂度成为瓶颈。继而，Tang等人[2]通过引入离散随机蛙跳算法（DSFLA）（discrete Shuffled Frog-Leaping Algorithm），强化了种群局部搜索和跳跃能力，但依赖度中心性决策使方法容易早熟，难以跳出局部最优。Khatri等人[3]则提出离散哈里斯鹰优化，通过自适应初始化和邻域检测增强对社区结构的发掘能力。
在进化算法方向，Qiu等人[4]提出局部影响力下降差分进化算法，结合局部影响力下降策略提升了种子节点选择精度，但高复杂度的影响力估算影响效率。为此，Biswas等人[5]采用两阶段VIKOR辅助差分进化，通过候选池压缩和多变异算子提升大规模网络的搜索效率，但仍面临候选池质量瓶颈。Wang等人[6]的多变换进化框架融合多算子以提升多样性与自适应性，但算子调度和参数整定复杂。Chen等[7]的IMUNE面向动态网络场景，针对无人机网络结构的时空变化，设计了动态候选集更新和种子选择机制，能够适应网络拓扑的频繁变动，从而提升了在时变环境下的影响力覆盖性能。Zhu等人[8]的PHEE则通过分阶段评估和模拟退火，进一步平衡了解的多样性与收敛性。
总体来看，元启发式算法通过提升搜索全局性和减少对高成本模拟的依赖，在影响力最大化问题上展现出显著优势，但由于缺乏对搜索空间结构的感知，往往表现为盲目搜索，容易陷入局部最优且难以突破搜索瓶颈。为此，本文引入适应度地形分析，通过刻画解空间结构特性，引导搜索行为跨越盆地障碍，从而系统增强全局寻优性能与解的多样性。 
2.5 适应度地形分析
适应度地形理论自Wright首次提出以来，作为连接优化问题解空间结构与算法性能分析的核心工具，一直推动着从进化生物学到现代智能优化领域的理论和实践进步[1,2]。 Stadler提出的三元组建模方法为连续与组合优化的地形分析奠定了坚实基础[2]。近年来，适应度地形分析方法在理论建模、度量工具和实际应用领域均获得了快速发展。
适应度地形分析已广泛应用于实际优化问题。动态优化与进化过程中的地形可变性和鲁棒性被关注，如地形旋转策略通过调整目标映射增强算法的动态适应能力[4]，生物进化研究发现杂交与新突变可降低地形崎岖度、提升可达性[5]，最新实验证据则表明即便在多峰崎岖地形中，高适应度峰仍能有效引导种群演化[6]。在组合优化与调度领域，编码方式和邻域结构直接影响地形属性和搜索表现，为实际问题中算法选择与参数调整提供了理论支撑[8]。多目标调度中，基于地形特性的模型显著提升了分布式异构柔性车间调度的优化效果[9]。Ochoa等人系统揭示了多目标解空间的多漏斗结构，为多目标优化算法的结构设计与性能评价提供了新视角[7]。此外，地形分析的准确性与代表性高度依赖于采样策略。文献表明，不同离散采样方法（如拉丁超立方采样LHS）会显著影响地形分析结果的代表性[12]；地形状态值等结构性指标已被用于提升群体智能算法的初始化与搜索性能[13]。这些进展为本研究算法设计提供了有价值的参考。
最近，已有研究首次利用适应度地形分析探查社交网络中关键节点的地形分布特征，为IM问题的高效求解提供了理论支持[14]。因此，利用适应度地形技术揭示解空间中的解的分布特性，有助于增强算法对解空间结构的感知与自适应调节能力，为影响力最大化问题的高效优化提供有力支撑。
3.提出的算法
3.1 问题建模与符号定义
社交网络影响力最大化问题旨在给定一个无向图 G=(V,E)，在节点集合 V 中选取 k 个种子节点 S⊆V,∣S∣=k，使得在特定的传播模型下，信息从 S出发能够激活的节点数最大。其数学表达为：















