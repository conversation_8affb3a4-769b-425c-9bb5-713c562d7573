# 第三部分：基于地形状态感知的自适应差分进化影响力最大化算法

## 3.1 问题建模

### 3.1.1 问题数学建模

给定社交网络G=(V,E)，其中V为节点集合，|V|=n，E为边集合。影响力最大化问题的目标是在节点集合V中选择k个种子节点S⊆V，|S|=k，使得在独立级联（IC）模型下的期望影响力最大：

```
max_{S⊆V, |S|=k} σ(S)
```

其中σ(S)表示种子集合S在IC模型下的期望影响力。

在IC模型中，信息传播过程如下：
1. 在时刻t=0，种子节点集合S被激活
2. 在时刻t≥1，每个在时刻t-1新激活的节点u尝试激活其每个未激活邻居v，成功概率为p_uv
3. 如果节点v被激活，则在下一时刻t+1参与传播过程
4. 传播过程持续直到没有新节点被激活

### 3.1.2 编码方案

算法采用整数向量编码方案，每个个体表示为长度为k的整数向量：

```
X_i = [x_{i,1}, x_{i,2}, ..., x_{i,k}]
```

其中x_{i,j}∈V且x_{i,j}≠x_{i,l}（当j≠l时），确保种子节点的唯一性。为提高计算效率，算法将原始网络的节点ID映射为连续整数{0, 1, 2, ..., n-1}。

## 3.2 算法总体框架

LADE算法的核心思想是通过适应度地形分析技术感知当前搜索状态，并据此自适应调整搜索策略。算法整体框架主要包含以下关键模块：

1. **混合初始化模块**：结合拉丁超立方采样（LHS）和基于度中心性的启发式采样，生成高质量且多样化的初始种群
2. **适应度评估模块**：采用局部影响力估计（LIE）函数进行快速准确的影响力评估
3. **地形状态感知模块**：通过加权PDI距离计算地形状态值λ，实时感知搜索空间结构特征
4. **状态驱动的算子调度模块**：基于λ值划分四种搜索状态，自适应选择相应的差分进化算子
5. **参数自适应模块**：动态调整交叉概率CR和缩放因子F，增强算法的自适应能力
6. **局部搜索模块**：对优质解进行基于LFV的邻域搜索，进一步提升解的质量

## 3.3 采样与初始化策略

为了生成高质量且多样化的初始种群，LADE采用混合初始化策略，结合拉丁超立方采样和基于度中心性的启发式采样。

### 3.3.1 基于LHS的空间采样

首先，算法通过网络直径路径将图划分为多个区域：

**算法1：基于直径的网络区域划分**
```
输入：网络图G=(V,E)
输出：区域划分R = {R_0, R_1, ..., R_d}

1. 获取最大连通子图G_lcc
2. 随机选择起点u∈V_lcc
3. 计算v = argmax_{w∈V_lcc} d(u,w)
4. 计算u' = argmax_{w∈V_lcc} d(v,w)
5. 获取直径路径P = shortest_path(u', v)
6. R_0 ← P
7. for 每个节点w∈V_lcc\P:
8.     dist ← min_{p∈P} d(w,p)
9.     R_dist ← R_dist ∪ {w}
10. return R
```

基于区域划分，使用拉丁超立方采样生成初始解：

**算法2：基于LHS的初始解生成**
```
输入：区域划分R，种子数k，采样数SN，桥节点B，综合评分scores
输出：LHS解集合S_LHS

1. S_LHS ← ∅
2. for i = 1 to SN:
3.     dims ← min(k, |R|)
4.     sample ← LHS(dims, 1)
5.     solution ← ∅
6.     for j = 1 to dims:
7.         region ← R_j
8.         idx ← ⌊sample[j] × |region|⌋
9.         solution ← solution ∪ {region[idx]}
10.    if |solution| < k:
11.        supplement ← strict_supplement(solution, k-|solution|, R, B, scores)
12.        solution ← solution ∪ supplement
13.    S_LHS ← S_LHS ∪ {solution}
14. return S_LHS
```

### 3.3.2 基于度中心性的启发式采样

为了平衡解的质量，算法同时生成基于度中心性的启发式解：

```
Score(v) = degree(v) + ε
```

其中ε~N(0,σ²)为高斯扰动项，增强解的多样性。

**算法3：基于度中心性的采样**
```
输入：网络图G，种子数k，采样数SN
输出：度中心性解集合S_score

1. S_score ← ∅
2. for i = 1 to SN:
3.     top_k ← degree_ranking(G, k)  // 获取度最高的k个节点
4.     solution ← top_k
5.     对solution中的部分节点进行随机替换  // 增加多样性
6.     S_score ← S_score ∪ {solution}
7. return S_score
```

### 3.3.3 质量与多样性筛选

初始化过程包含并行适应度计算、质量筛选和多样性筛选三个阶段：

1. **并行适应度计算**：使用多进程并行计算所有候选解的LIE适应度值，构建适应度缓存
2. **质量筛选**：基于适应度值选择前70%的高质量解
3. **多样性筛选**：使用Jaccard相似度度量解之间的相似性，过滤相似度超过阈值θ=0.8的解

相似度计算公式为：
```
Similarity(S_1, S_2) = |S_1 ∩ S_2| / |S_1 ∪ S_2|
```

## 3.4 适应度评估

算法采用局部影响力估计（LIE）函数进行适应度评估，该函数基于二跳传播模型，能够在保证计算效率的同时提供较为准确的影响力估计。

### 3.4.1 LIE函数计算

LIE函数的计算分为三个部分：

#### 种子节点贡献
种子节点直接被激活，贡献值为：
```
C_0(S) = |S|
```

#### 一跳邻居贡献
对于种子集合S的一跳邻居节点v∈N_1(S)\S，其被激活的概率为：
```
P_1(v) = 1 - ∏_{u∈S∩N(v)} (1-p)
```

一跳邻居的总贡献为：
```
C_1(S) = Σ_{v∈N_1(S)\S} P_1(v)
```

#### 二跳邻居贡献
对于二跳邻居节点v∈N_2(S)\(S∪N_1(S))，需要考虑通过一跳邻居被激活的概率。设M(v)为连接到节点v的一跳邻居集合，则：

```
P_2(v) = 1 - ∏_{u∈M(v)} (1 - p · P_1(u))
```

二跳邻居的总贡献为：
```
C_2(S) = Σ_{v∈N_2(S)\(S∪N_1(S))} P_2(v)
```

#### LIE函数总公式
综合三部分贡献，LIE函数定义为：
```
LIE(S) = C_0(S) + C_1(S) + C_2(S)
```

### 3.4.2 适应度缓存机制

为提高计算效率，算法采用基于哈希的缓存机制：

**算法4：适应度缓存机制**
```
输入：种子集合S，网络图G，激活概率p
输出：适应度值fitness

1. key ← hash(sorted(S), |V|, |E|, p)
2. if key ∈ cache:
3.     return cache[key]
4. else:
5.     fitness ← LIE(S, G, p)
6.     cache[key] ← fitness
7.     if |cache| > max_size:
8.         清理最旧的缓存项
9.     return fitness
```

## 3.5 地形状态感知机制

地形状态感知是LADE算法的核心创新，通过计算地形状态值λ来刻画当前搜索状态。

### 3.5.1 加权PDI距离计算

算法采用基于LFV（局部影响力值）的加权PDI距离来度量个体间的差异：

```
PDI(S_1, S_2) = Σ_{v∈S_1△S_2} LFV(v) / Σ_{v∈S_1∪S_2} LFV(v)
```

其中S_1△S_2 = (S_1\S_2)∪(S_2\S_1)为对称差集，LFV(v)为节点v的局部影响力值：

```
LFV(v) = 1 + Σ_{u∈N(v)} (p + p · Σ_{w∈N(u)\{v}} p)
```

### 3.5.2 地形状态值计算

地形状态值λ的计算融合了个体分布特征和适应度信息：

```
λ = (d_g - d_min) / (d_max - d_min)
```

其中：
- d_g：当前最优个体与种群中其他个体的平均加权PDI距离
- d_max：种群中个体间的最大平均加权PDI距离
- d_min：种群中个体间的最小平均加权PDI距离

### 3.5.3 动态阈值计算

为了适应不同网络的特征，算法采用动态阈值划分搜索状态。基于历史λ值计算四分位数：

```
Q_1 = Percentile(Λ_history, 25)
Q_3 = Percentile(Λ_history, 75)
```

其中Λ_history为历史λ值序列。

### 3.5.4 搜索状态划分

基于λ值和动态阈值，算法将搜索过程划分为四种状态：

1. **收敛状态**（Convergence）：λ∈[0, Q_1)，种群高度聚集，需要精细搜索
2. **开发状态**（Exploitation）：λ∈[Q_1, (Q_1+Q_3)/2)，在有希望区域深入搜索
3. **探索状态**（Exploration）：λ∈[(Q_1+Q_3)/2, Q_3)，扩大搜索范围
4. **逃逸状态**（Escape）：λ∈[Q_3, 1]或满足逃逸条件，跳出局部最优

逃逸条件定义为：
```
Escape_Condition = (λ < 0.1) ∧ (stagnation_counter ≥ threshold)
```

## 3.6 主循环：变异、交叉、选择与参数更新

LADE算法的主循环包含地形状态分析、状态驱动的变异、交叉、选择和参数自适应更新等关键步骤。

### 3.6.1 状态驱动的变异算子

针对四种搜索状态，算法设计了相应的变异策略：

#### 收敛状态变异算子（DE/best/1）

在收敛状态下，种群高度聚集，采用基于最优个体的变异策略进行精细搜索：

**算法5：收敛状态变异算子**
```
输入：目标个体X_i，最优个体X_best，缩放因子F
输出：变异个体V_i

1. 随机选择两个不同个体X_r1, X_r2
2. 计算差异集D = X_r1 △ X_r2
3. N_R ← ⌊F × |D|⌋
4. V_i ← X_best
5. for j = 1 to N_R:
6.     if D ≠ ∅:
7.         随机选择node ∈ D
8.         找到V_i中LFV最小的节点min_node
9.         V_i[V_i.index(min_node)] ← node
10.        D ← D \ {node}
11. return V_i
```

#### 开发状态变异算子（DE/current-to-best/1）

在开发状态下，结合当前个体和最优个体信息，平衡局部搜索和全局引导：

**算法6：开发状态变异算子**
```
输入：目标个体X_i，最优个体X_best，缩放因子F
输出：变异个体V_i

1. 随机选择两个不同个体X_r1, X_r2
2. 计算差异集D_1 = X_best \ X_i，D_2 = X_r1 △ X_r2
3. 合并差异集D = D_1 ∪ D_2
4. N_R ← ⌊F × |D|⌋
5. V_i ← X_i
6. for j = 1 to N_R:
7.     if D ≠ ∅:
8.         随机选择node ∈ D
9.         找到V_i中LFV最小的节点min_node
10.        V_i[V_i.index(min_node)] ← node
11.        D ← D \ {node}
12. return V_i
```

#### 探索状态变异算子（DE/rand/1）

在探索状态下，采用随机变异策略扩大搜索范围。

#### 逃逸状态变异算子

在逃逸状态下，采用基于逃逸候选池的扰动变异策略。

### 3.6.2 交叉算子

算法采用二项式交叉策略：

**算法7：二项式交叉算子**
```
输入：目标个体X_i，变异个体V_i，交叉概率CR，种子数k
输出：试验个体U_i

1. U_i ← X_i
2. for j = 1 to k:
3.     if random() ≤ CR:
4.         U_i[j] ← V_i[j]
5. 检查并修复重复节点
6. return U_i
```

### 3.6.3 选择算子

算法采用贪婪选择策略：

**算法8：选择算子**
```
输入：目标个体X_i，试验个体U_i，参数CR，F
输出：下一代个体X_i^(g+1)

1. fitness_trial ← LIE(U_i)
2. fitness_target ← LIE(X_i)
3. if fitness_trial ≥ fitness_target:
4.     记录成功参数：S_CR ← S_CR ∪ {CR}，S_F ← S_F ∪ {F}
5.     return U_i
6. else:
7.     return X_i
```

### 3.6.4 参数自适应更新

LADE算法采用自适应参数控制策略，动态调整交叉概率CR和缩放因子F。

#### 参数生成

交叉概率CR服从正态分布：
```
CR ~ N(μ_CR, 0.1²)
```

缩放因子F服从柯西分布：
```
F ~ Cauchy(μ_F, 0.1)
```

#### 参数更新

基于成功个体的参数值更新分布参数：

```
μ_CR^(g+1) = (1-c) · μ_CR^(g) + c · mean(S_CR)
μ_F^(g+1) = (1-c) · μ_F^(g) + c · mean(S_F)
```

其中c=0.1为学习率，S_CR和S_F为成功参数集合。

## 3.7 局部搜索

为了进一步提升解的质量，LADE算法设计了基于LFV的高效局部搜索机制。

### 3.7.1 局部搜索策略

局部搜索采用基于邻域的贪婪改进策略：

**算法9：基于LFV的局部搜索**
```
输入：个体X，最大邻居数max_neighbors
输出：优化后的个体X'

1. X' ← X，best_fitness ← LIE(X)
2. found_improvement ← false
3. 按LFV值升序排序X中的节点得到sorted_nodes
4. for 每个节点node ∈ sorted_nodes:
5.     neighbors ← get_top_neighbors(node, max_neighbors)
6.     for 每个邻居neighbor ∈ neighbors:
7.         if neighbor ∈ X':
8.             continue
9.         生成新解：X_new[i] ← neighbor if X'[i] = node
10.        new_fitness ← LIE(X_new)
11.        if new_fitness > best_fitness:
12.            X' ← X_new，best_fitness ← new_fitness
13.            found_improvement ← true
14.            break  // 找到改进即跳出
15. return X'
```

### 3.7.2 局部搜索应用

算法在两个关键位置应用局部搜索：

1. **种群优化**：对每代前10%的优质个体进行局部搜索，参数max_neighbors=8
2. **全局最优优化**：对当前全局最优个体进行深度局部搜索，参数max_neighbors=10

## 3.8 LADE算法完整流程

**算法10：LADE算法主流程**
```
输入：网络图G=(V,E)，种子数k，种群大小NP，最大函数评估次数FEs_max，激活概率p
输出：最优种子集合S*

// 阶段1：预处理
1. bridge_nodes ← detect_bridge_nodes(G)
2. combined_scores ← calculate_combined_centrality(G)
3. G ← convert_node_labels_to_integers(G)

// 阶段2：混合初始化
4. regions ← divide_by_diameter(G)
5. S_LHS ← sample_lhs(G, k, NP/2, bridge_nodes, combined_scores)
6. S_score ← sample_score(G, k, NP/2)
7. all_solutions ← S_LHS ∪ S_score
8. fitness_cache ← parallel_compute_fitness(all_solutions, G, p)
9. P^(0) ← initialize_population_hybrid(S_LHS, S_score, NP)

// 阶段3：参数初始化
10. μ_CR ← 0.5，μ_F ← 0.5，c ← 0.1
11. Λ_history ← ∅，g ← 0，FEs ← 0
12. λ^(0) ← compute_lambda(P^(0))
13. Λ_history ← Λ_history ∪ {λ^(0)}

// 阶段4：主进化循环
14. while FEs < FEs_max AND g < G_max:
15.     g ← g + 1
16.     λ^(g) ← compute_lambda(P^(g-1))
17.     Λ_history ← Λ_history ∪ {λ^(g)}
18.     current_state ← determine_state(λ^(g), Λ_history)
19.
20.     S_CR ← ∅，S_F ← ∅
21.     new_population ← ∅
22.     for 每个个体X_i ∈ P^(g-1):
23.         CR_i, F_i ← generate_parameters(μ_CR, μ_F)
24.         V_i ← select_mutation(X_i, current_state, F_i)
25.         U_i ← crossover(X_i, V_i, CR_i, k)
26.         X_i^(g) ← selection(X_i, U_i, CR_i, F_i)
27.         new_population ← new_population ∪ {X_i^(g)}
28.         更新FEs计数器
29.
30.     // 局部搜索
31.     num_to_optimize ← max(1, ⌊0.1 × NP⌋)
32.     candidates ← top_individuals(new_population, num_to_optimize)
33.     for 每个候选个体candidate ∈ candidates:
34.         optimized ← local_search(candidate, 8)
35.         if LIE(optimized) > LIE(candidate):
36.             更新new_population中对应个体
37.
38.     // 全局最优局部搜索
39.     G_best ← argmax_{X∈new_population} LIE(X)
40.     G_best ← local_search(G_best, 10)
41.
42.     // 参数更新
43.     if S_CR ≠ ∅ AND S_F ≠ ∅:
44.         μ_CR ← (1-c) · μ_CR + c · mean(S_CR)
45.         μ_F ← (1-c) · μ_F + c · mean(S_F)
46.
47.     P^(g) ← new_population
48.
49. return S* ← argmax_{X∈P^(g)} LIE(X)
```

## 3.9 算法复杂度分析

### 3.9.1 时间复杂度

LADE算法的时间复杂度主要由以下几个部分构成：

1. **预处理阶段**：
   - 桥节点检测：O(n·m)（介数中心性计算）
   - 网络区域划分：O(n²)（最短路径计算）
   - 节点ID连续化：O(n + m)

2. **初始化阶段**：
   - LHS采样：O(SN·k)
   - 度中心性采样：O(SN·k)
   - 并行适应度计算：O(SN·k·d̄)，其中d̄为平均度
   - 质量与多样性筛选：O(SN²·k)

3. **主进化循环**（每代）：
   - 地形状态值计算：O(NP²·k)（距离矩阵计算）
   - 变异操作：O(NP·k)
   - 交叉操作：O(NP·k)
   - 选择操作：O(NP·k·d̄)（适应度评估）
   - 局部搜索：O(0.1·NP·k·max_neighbors·d̄)

总时间复杂度为：
```
T(n,k,NP,G_max) = O(n² + G_max · NP · (NP · k + k · d̄))
```

在实际应用中，由于采用了适应度缓存机制和并行计算，实际运行时间显著优于理论复杂度。

### 3.9.2 空间复杂度

算法的空间复杂度主要包括：
- 种群存储：O(NP·k)
- 网络存储：O(n + m)，其中m为边数
- LFV缓存：O(n)
- 适应度缓存：O(C)，其中C为缓存大小
- 距离矩阵：O(NP²)
- 辅助数据结构：O(n + k)

总空间复杂度为：O(NP² + NP·k + n + m + C)

## 3.10 本章小结

本章详细介绍了基于地形状态感知的自适应差分进化算法（LADE）。该算法针对影响力最大化问题的特点，设计了完整的求解框架：

1. **问题建模**：采用整数向量编码方案，将影响力最大化问题转化为组合优化问题

2. **混合初始化**：结合LHS采样和度中心性采样，通过质量与多样性筛选生成高质量初始种群

3. **适应度评估**：采用LIE函数进行快速准确的影响力估计，配合缓存机制提高计算效率

4. **地形状态感知**：通过加权PDI距离计算地形状态值λ，实现搜索状态的实时感知和动态阈值划分

5. **状态驱动的主循环**：基于四种搜索状态自适应选择变异算子，配合二项式交叉和贪婪选择，实现高效的种群进化

6. **参数自适应**：采用正态分布和柯西分布生成CR和F参数，基于成功经验进行参数更新

7. **局部搜索**：对优质个体进行基于LFV的邻域搜索，进一步提升解的质量

算法的主要创新点包括：
- 首次将适应度地形理论引入影响力最大化问题，实现搜索状态的量化感知
- 设计了基于LFV的加权PDI距离度量，更准确地刻画解空间结构
- 提出了四态搜索机制，实现搜索策略的自适应调整
- 集成了高效的局部搜索机制，平衡了全局探索和局部开发

通过理论分析和算法设计，LADE算法为解决影响力最大化问题提供了一种新的有效途径，具有良好的理论基础和实用价值。
