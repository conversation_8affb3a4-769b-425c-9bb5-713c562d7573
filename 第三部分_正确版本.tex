\documentclass[12pt]{article}
\usepackage[UTF8]{ctex}
\usepackage{amsmath}
\usepackage{amsfonts}
\usepackage{amssymb}
\usepackage{algorithm}
\usepackage{algorithmic}
\usepackage{graphicx}
\usepackage{geometry}
\usepackage{booktabs}
\usepackage{multirow}
\usepackage{array}
\usepackage{float}
\usepackage{enumerate}

\geometry{a4paper,left=2.5cm,right=2.5cm,top=2.5cm,bottom=2.5cm}

\renewcommand{\algorithmicrequire}{\textbf{输入:}}
\renewcommand{\algorithmicensure}{\textbf{输出:}}

\title{第三部分：基于地形状态感知的自适应差分进化影响力最大化算法}
\author{}
\date{}

\begin{document}

\maketitle

\section{所提算法}

本节详细介绍基于地形状态感知的自适应差分进化算法（Landscape-Aware Adaptive Differential Evolution, LADE）。该算法通过构建地形状态建模机制，实现搜索行为与解空间结构的动态耦合，并设计状态驱动的算子调度策略，显著提升影响力最大化问题的求解性能。

\subsection{问题建模}

\subsubsection{问题数学建模}

给定社交网络$G=(V,E)$，其中$V$为节点集合，$|V|=n$，$E$为边集合。影响力最大化问题的目标是在节点集合$V$中选择$k$个种子节点$S \subseteq V$，$|S|=k$，使得在独立级联（IC）模型下的期望影响力最大：

\begin{equation}
\max_{S \subseteq V, |S|=k} \sigma(S)
\end{equation}

其中$\sigma(S)$表示种子集合$S$在IC模型下的期望影响力。

在IC模型中，信息传播过程如下：
\begin{enumerate}
\item 在时刻$t=0$，种子节点集合$S$被激活
\item 在时刻$t \geq 1$，每个在时刻$t-1$新激活的节点$u$尝试激活其每个未激活邻居$v$，成功概率为$p_{uv}$
\item 如果节点$v$被激活，则在下一时刻$t+1$参与传播过程
\item 传播过程持续直到没有新节点被激活
\end{enumerate}

\subsubsection{编码方案}

算法采用整数向量编码方案，每个个体表示为长度为$k$的整数向量：
\begin{equation}
X_i = [x_{i,1}, x_{i,2}, \ldots, x_{i,k}]
\end{equation}

其中$x_{i,j} \in V$且$x_{i,j} \neq x_{i,l}$（当$j \neq l$时），确保种子节点的唯一性。为提高计算效率，算法将原始网络的节点ID映射为连续整数$\{0, 1, 2, \ldots, n-1\}$。

\subsection{算法总体框架}

LADE算法的核心思想是通过适应度地形分析技术感知当前搜索状态，并据此自适应调整搜索策略。算法整体框架主要包含以下关键模块：

\begin{enumerate}
\item \textbf{混合初始化模块}：结合拉丁超立方采样（LHS）和基于度中心性的启发式采样，生成高质量且多样化的初始种群
\item \textbf{适应度评估模块}：采用局部影响力估计（LIE）函数进行快速准确的影响力评估
\item \textbf{地形状态感知模块}：通过加权PDI距离计算地形状态值$\lambda$，实时感知搜索空间结构特征
\item \textbf{状态驱动的算子调度模块}：基于$\lambda$值划分四种搜索状态，自适应选择相应的差分进化算子
\item \textbf{参数自适应模块}：动态调整交叉概率$CR$和缩放因子$F$，增强算法的自适应能力
\item \textbf{局部搜索模块}：对优质解进行基于LFV的邻域搜索，进一步提升解的质量
\end{enumerate}

\subsection{采样与初始化策略}

为了生成高质量且多样化的初始种群，LADE采用混合初始化策略，结合拉丁超立方采样和基于度中心性的启发式采样。

\subsubsection{基于LHS的空间采样}

首先，算法通过网络直径路径将图划分为多个区域：

\begin{algorithm}[H]
\caption{基于直径的网络区域划分}
\begin{algorithmic}[1]
\REQUIRE 网络图$G=(V,E)$
\ENSURE 区域划分$\mathcal{R} = \{R_0, R_1, \ldots, R_d\}$
\STATE 获取最大连通子图$G_{lcc}$
\STATE 随机选择起点$u \in V_{lcc}$
\STATE 计算$v = \arg\max_{w \in V_{lcc}} d(u,w)$
\STATE 计算$u' = \arg\max_{w \in V_{lcc}} d(v,w)$
\STATE 获取直径路径$P = \text{shortest\_path}(u', v)$
\STATE $R_0 \leftarrow P$
\FOR{每个节点$w \in V_{lcc} \setminus P$}
    \STATE $dist \leftarrow \min_{p \in P} d(w,p)$
    \STATE $R_{dist} \leftarrow R_{dist} \cup \{w\}$
\ENDFOR
\RETURN $\mathcal{R}$
\end{algorithmic}
\end{algorithm}

基于区域划分，使用拉丁超立方采样生成初始解：

\begin{algorithm}[H]
\caption{基于LHS的初始解生成}
\begin{algorithmic}[1]
\REQUIRE 区域划分$\mathcal{R}$，种子数$k$，采样数$SN$，桥节点$B$，综合评分$scores$
\ENSURE LHS解集合$\mathcal{S}_{LHS}$
\STATE $\mathcal{S}_{LHS} \leftarrow \emptyset$
\FOR{$i = 1$ to $SN$}
    \STATE $dims \leftarrow \min(k, |\mathcal{R}|)$
    \STATE $sample \leftarrow \text{LHS}(dims, 1)$
    \STATE $solution \leftarrow \emptyset$
    \FOR{$j = 1$ to $dims$}
        \STATE $region \leftarrow R_j$
        \STATE $idx \leftarrow \lfloor sample[j] \times |region| \rfloor$
        \STATE $solution \leftarrow solution \cup \{region[idx]\}$
    \ENDFOR
    \IF{$|solution| < k$}
        \STATE $supplement \leftarrow \text{strict\_supplement}(solution, k-|solution|, \mathcal{R}, B, scores)$
        \STATE $solution \leftarrow solution \cup supplement$
    \ENDIF
    \STATE $\mathcal{S}_{LHS} \leftarrow \mathcal{S}_{LHS} \cup \{solution\}$
\ENDFOR
\RETURN $\mathcal{S}_{LHS}$
\end{algorithmic}
\end{algorithm}

\subsubsection{基于度中心性的启发式采样}

为了平衡解的质量，算法同时生成基于度中心性的启发式解：

\begin{equation}
\text{Score}(v) = \text{degree}(v) + \epsilon
\end{equation}

其中$\epsilon \sim \mathcal{N}(0, \sigma^2)$为高斯扰动项，增强解的多样性。

\begin{algorithm}[H]
\caption{基于度中心性的采样}
\begin{algorithmic}[1]
\REQUIRE 网络图$G$，种子数$k$，采样数$SN$
\ENSURE 度中心性解集合$\mathcal{S}_{score}$
\STATE $\mathcal{S}_{score} \leftarrow \emptyset$
\FOR{$i = 1$ to $SN$}
    \STATE $top\_k \leftarrow \text{degree\_ranking}(G, k)$ // 获取度最高的k个节点
    \STATE $solution \leftarrow top\_k$
    \STATE 对$solution$中的部分节点进行随机替换 // 增加多样性
    \STATE $\mathcal{S}_{score} \leftarrow \mathcal{S}_{score} \cup \{solution\}$
\ENDFOR
\RETURN $\mathcal{S}_{score}$
\end{algorithmic}
\end{algorithm}

\subsubsection{质量与多样性筛选}

初始化过程包含并行适应度计算、质量筛选和多样性筛选三个阶段：

\begin{enumerate}
\item \textbf{并行适应度计算}：使用多进程并行计算所有候选解的LIE适应度值，构建适应度缓存
\item \textbf{质量筛选}：基于适应度值选择前70\%的高质量解
\item \textbf{多样性筛选}：使用Jaccard相似度度量解之间的相似性，过滤相似度超过阈值$\theta=0.8$的解
\end{enumerate}

相似度计算公式为：
\begin{equation}
\text{Similarity}(S_1, S_2) = \frac{|S_1 \cap S_2|}{|S_1 \cup S_2|}
\end{equation}

\subsection{适应度评估}

算法采用局部影响力估计（LIE）函数进行适应度评估，该函数基于二跳传播模型，能够在保证计算效率的同时提供较为准确的影响力估计。

\subsubsection{LIE函数计算}

LIE函数的计算分为三个部分：

\paragraph{种子节点贡献}
种子节点直接被激活，贡献值为：
\begin{equation}
C_0(S) = |S|
\end{equation}

\paragraph{一跳邻居贡献}
对于种子集合$S$的一跳邻居节点$v \in N_1(S) \setminus S$，其被激活的概率为：
\begin{equation}
P_1(v) = 1 - \prod_{u \in S \cap N(v)} (1-p)
\end{equation}

一跳邻居的总贡献为：
\begin{equation}
C_1(S) = \sum_{v \in N_1(S) \setminus S} P_1(v)
\end{equation}

\paragraph{二跳邻居贡献}
对于二跳邻居节点$v \in N_2(S) \setminus (S \cup N_1(S))$，需要考虑通过一跳邻居被激活的概率。设$M(v)$为连接到节点$v$的一跳邻居集合，则：

\begin{equation}
P_2(v) = 1 - \prod_{u \in M(v)} (1 - p \cdot P_1(u))
\end{equation}

二跳邻居的总贡献为：
\begin{equation}
C_2(S) = \sum_{v \in N_2(S) \setminus (S \cup N_1(S))} P_2(v)
\end{equation}

\paragraph{LIE函数总公式}
综合三部分贡献，LIE函数定义为：
\begin{equation}
\text{LIE}(S) = C_0(S) + C_1(S) + C_2(S)
\end{equation}

\subsubsection{适应度缓存机制}

为提高计算效率，算法采用基于哈希的缓存机制：

\begin{algorithm}[H]
\caption{适应度缓存机制}
\begin{algorithmic}[1]
\REQUIRE 种子集合$S$，网络图$G$，激活概率$p$
\ENSURE 适应度值$fitness$
\STATE $key \leftarrow \text{hash}(\text{sorted}(S), |V|, |E|, p)$
\IF{$key \in \text{cache}$}
    \RETURN $\text{cache}[key]$
\ELSE
    \STATE $fitness \leftarrow \text{LIE}(S, G, p)$
    \STATE $\text{cache}[key] \leftarrow fitness$
    \IF{$|\text{cache}| > \text{max\_size}$}
        \STATE 清理最旧的缓存项
    \ENDIF
    \RETURN $fitness$
\ENDIF
\end{algorithmic}
\end{algorithm}

\end{document}
