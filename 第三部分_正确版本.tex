\documentclass[12pt]{article}
\usepackage[UTF8]{ctex}
\usepackage{amsmath}
\usepackage{amsfonts}
\usepackage{amssymb}
\usepackage{algorithm}
\usepackage{algorithmic}
\usepackage{graphicx}
\usepackage{geometry}
\usepackage{booktabs}
\usepackage{multirow}
\usepackage{array}
\usepackage{float}
\usepackage{enumerate}

\geometry{a4paper,left=2.5cm,right=2.5cm,top=2.5cm,bottom=2.5cm}

\renewcommand{\algorithmicrequire}{\textbf{输入:}}
\renewcommand{\algorithmicensure}{\textbf{输出:}}

\title{第三部分：基于地形状态感知的自适应差分进化影响力最大化算法}
\author{}
\date{}

\begin{document}

\maketitle

\section{所提算法}

本节详细介绍基于地形状态感知的自适应差分进化算法（Landscape-Aware Adaptive Differential Evolution, LADE）。该算法通过构建地形状态建模机制，实现搜索行为与解空间结构的动态耦合，并设计状态驱动的算子调度策略，显著提升影响力最大化问题的求解性能。

\subsection{问题建模}

\subsubsection{问题数学建模}

给定社交网络$G=(V,E)$，其中$V$为节点集合，$|V|=n$，$E$为边集合。影响力最大化问题的目标是在节点集合$V$中选择$k$个种子节点$S \subseteq V$，$|S|=k$，使得在独立级联（IC）模型下的期望影响力最大：

\begin{equation}
\max_{S \subseteq V, |S|=k} \sigma(S)
\end{equation}

其中$\sigma(S)$表示种子集合$S$在IC模型下的期望影响力。

在IC模型中，信息传播过程如下：
\begin{enumerate}
\item 在时刻$t=0$，种子节点集合$S$被激活
\item 在时刻$t \geq 1$，每个在时刻$t-1$新激活的节点$u$尝试激活其每个未激活邻居$v$，成功概率为$p_{uv}$
\item 如果节点$v$被激活，则在下一时刻$t+1$参与传播过程
\item 传播过程持续直到没有新节点被激活
\end{enumerate}

\subsubsection{编码方案}

算法采用整数向量编码方案，每个个体表示为长度为$k$的整数向量：
\begin{equation}
X_i = [x_{i,1}, x_{i,2}, \ldots, x_{i,k}]
\end{equation}

其中$x_{i,j} \in V$且$x_{i,j} \neq x_{i,l}$（当$j \neq l$时），确保种子节点的唯一性。为提高计算效率，算法将原始网络的节点ID映射为连续整数$\{0, 1, 2, \ldots, n-1\}$。

\subsection{算法总体框架}

LADE算法的核心思想是通过适应度地形分析技术感知当前搜索状态，并据此自适应调整搜索策略。算法整体框架主要包含以下关键模块：

\begin{enumerate}
\item \textbf{混合初始化模块}：结合拉丁超立方采样（LHS）和基于度中心性的启发式采样，生成高质量且多样化的初始种群
\item \textbf{适应度评估模块}：采用局部影响力估计（LIE）函数进行快速准确的影响力评估
\item \textbf{地形状态感知模块}：通过加权PDI距离计算地形状态值$\lambda$，实时感知搜索空间结构特征
\item \textbf{状态驱动的算子调度模块}：基于$\lambda$值划分四种搜索状态，自适应选择相应的差分进化算子
\item \textbf{参数自适应模块}：动态调整交叉概率$CR$和缩放因子$F$，增强算法的自适应能力
\item \textbf{局部搜索模块}：对优质解进行基于LFV的邻域搜索，进一步提升解的质量
\end{enumerate}

\subsection{采样与初始化策略}

为了生成高质量且多样化的初始种群，LADE采用混合初始化策略，结合拉丁超立方采样和基于度中心性的启发式采样。

\subsubsection{基于LHS的空间采样}

首先，算法通过网络直径路径将图划分为多个区域：

\begin{algorithm}[H]
\caption{基于直径的网络区域划分}
\begin{algorithmic}[1]
\REQUIRE 网络图$G=(V,E)$
\ENSURE 区域划分$\mathcal{R} = \{R_0, R_1, \ldots, R_d\}$
\STATE 获取最大连通子图$G_{lcc}$
\STATE 随机选择起点$u \in V_{lcc}$
\STATE 计算$v = \arg\max_{w \in V_{lcc}} d(u,w)$
\STATE 计算$u' = \arg\max_{w \in V_{lcc}} d(v,w)$
\STATE 获取直径路径$P = \text{shortest\_path}(u', v)$
\STATE $R_0 \leftarrow P$
\FOR{每个节点$w \in V_{lcc} \setminus P$}
    \STATE $dist \leftarrow \min_{p \in P} d(w,p)$
    \STATE $R_{dist} \leftarrow R_{dist} \cup \{w\}$
\ENDFOR
\RETURN $\mathcal{R}$
\end{algorithmic}
\end{algorithm}

基于区域划分，使用拉丁超立方采样生成初始解：

\begin{algorithm}[H]
\caption{基于LHS的初始解生成}
\begin{algorithmic}[1]
\REQUIRE 区域划分$\mathcal{R}$，种子数$k$，采样数$SN$，桥节点$B$，综合评分$scores$
\ENSURE LHS解集合$\mathcal{S}_{LHS}$
\STATE $\mathcal{S}_{LHS} \leftarrow \emptyset$
\FOR{$i = 1$ to $SN$}
    \STATE $dims \leftarrow \min(k, |\mathcal{R}|)$
    \STATE $sample \leftarrow \text{LHS}(dims, 1)$
    \STATE $solution \leftarrow \emptyset$
    \FOR{$j = 1$ to $dims$}
        \STATE $region \leftarrow R_j$
        \STATE $idx \leftarrow \lfloor sample[j] \times |region| \rfloor$
        \STATE $solution \leftarrow solution \cup \{region[idx]\}$
    \ENDFOR
    \IF{$|solution| < k$}
        \STATE $supplement \leftarrow \text{strict\_supplement}(solution, k-|solution|, \mathcal{R}, B, scores)$
        \STATE $solution \leftarrow solution \cup supplement$
    \ENDIF
    \STATE $\mathcal{S}_{LHS} \leftarrow \mathcal{S}_{LHS} \cup \{solution\}$
\ENDFOR
\RETURN $\mathcal{S}_{LHS}$
\end{algorithmic}
\end{algorithm}

\subsubsection{基于度中心性的启发式采样}

为了平衡解的质量，算法同时生成基于度中心性的启发式解：

\begin{equation}
\text{Score}(v) = \text{degree}(v) + \epsilon
\end{equation}

其中$\epsilon \sim \mathcal{N}(0, \sigma^2)$为高斯扰动项，增强解的多样性。

\begin{algorithm}[H]
\caption{基于度中心性的采样}
\begin{algorithmic}[1]
\REQUIRE 网络图$G$，种子数$k$，采样数$SN$
\ENSURE 度中心性解集合$\mathcal{S}_{score}$
\STATE $\mathcal{S}_{score} \leftarrow \emptyset$
\FOR{$i = 1$ to $SN$}
    \STATE $top\_k \leftarrow \text{degree\_ranking}(G, k)$ // 获取度最高的k个节点
    \STATE $solution \leftarrow top\_k$
    \STATE 对$solution$中的部分节点进行随机替换 // 增加多样性
    \STATE $\mathcal{S}_{score} \leftarrow \mathcal{S}_{score} \cup \{solution\}$
\ENDFOR
\RETURN $\mathcal{S}_{score}$
\end{algorithmic}
\end{algorithm}

\subsubsection{质量与多样性筛选}

初始化过程包含并行适应度计算、质量筛选和多样性筛选三个阶段：

\begin{enumerate}
\item \textbf{并行适应度计算}：使用多进程并行计算所有候选解的LIE适应度值，构建适应度缓存
\item \textbf{质量筛选}：基于适应度值选择前70\%的高质量解
\item \textbf{多样性筛选}：使用Jaccard相似度度量解之间的相似性，过滤相似度超过阈值$\theta=0.8$的解
\end{enumerate}

相似度计算公式为：
\begin{equation}
\text{Similarity}(S_1, S_2) = \frac{|S_1 \cap S_2|}{|S_1 \cup S_2|}
\end{equation}

\subsection{适应度评估}

算法采用局部影响力估计（LIE）函数进行适应度评估，该函数基于二跳传播模型，能够在保证计算效率的同时提供较为准确的影响力估计。

\subsubsection{LIE函数计算}

LIE函数的计算分为三个部分：

\paragraph{种子节点贡献}
种子节点直接被激活，贡献值为：
\begin{equation}
C_0(S) = |S|
\end{equation}

\paragraph{一跳邻居贡献}
对于种子集合$S$的一跳邻居节点$v \in N_1(S) \setminus S$，其被激活的概率为：
\begin{equation}
P_1(v) = 1 - \prod_{u \in S \cap N(v)} (1-p)
\end{equation}

一跳邻居的总贡献为：
\begin{equation}
C_1(S) = \sum_{v \in N_1(S) \setminus S} P_1(v)
\end{equation}

\paragraph{二跳邻居贡献}
对于二跳邻居节点$v \in N_2(S) \setminus (S \cup N_1(S))$，需要考虑通过一跳邻居被激活的概率。设$M(v)$为连接到节点$v$的一跳邻居集合，则：

\begin{equation}
P_2(v) = 1 - \prod_{u \in M(v)} (1 - p \cdot P_1(u))
\end{equation}

二跳邻居的总贡献为：
\begin{equation}
C_2(S) = \sum_{v \in N_2(S) \setminus (S \cup N_1(S))} P_2(v)
\end{equation}

\paragraph{LIE函数总公式}
综合三部分贡献，LIE函数定义为：
\begin{equation}
\text{LIE}(S) = C_0(S) + C_1(S) + C_2(S)
\end{equation}

\subsubsection{适应度缓存机制}

为提高计算效率，算法采用基于哈希的缓存机制：

\begin{algorithm}[H]
\caption{适应度缓存机制}
\begin{algorithmic}[1]
\REQUIRE 种子集合$S$，网络图$G$，激活概率$p$
\ENSURE 适应度值$fitness$
\STATE $key \leftarrow \text{hash}(\text{sorted}(S), |V|, |E|, p)$
\IF{$key \in \text{cache}$}
    \RETURN $\text{cache}[key]$
\ELSE
    \STATE $fitness \leftarrow \text{LIE}(S, G, p)$
    \STATE $\text{cache}[key] \leftarrow fitness$
    \IF{$|\text{cache}| > \text{max\_size}$}
        \STATE 清理最旧的缓存项
    \ENDIF
    \RETURN $fitness$
\ENDIF
\end{algorithmic}
\end{algorithm}

\subsection{地形状态感知机制}

地形状态感知是LADE算法的核心创新，通过计算地形状态值$\lambda$来刻画当前搜索状态。

\subsubsection{加权PDI距离计算}

算法采用基于LFV（局部影响力值）的加权PDI距离来度量个体间的差异：

\begin{equation}
\text{PDI}(S_1, S_2) = \frac{\sum_{v \in S_1 \triangle S_2} \text{LFV}(v)}{\sum_{v \in S_1 \cup S_2} \text{LFV}(v)}
\end{equation}

其中$S_1 \triangle S_2 = (S_1 \setminus S_2) \cup (S_2 \setminus S_1)$为对称差集，$\text{LFV}(v)$为节点$v$的局部影响力值：

\begin{equation}
\text{LFV}(v) = 1 + \sum_{u \in N(v)} \left( p + p \sum_{w \in N(u) \setminus \{v\}} p \right)
\end{equation}

\subsubsection{地形状态值计算}

地形状态值$\lambda$的计算融合了个体分布特征和适应度信息：

\begin{equation}
\lambda = \frac{d_g - d_{\min}}{d_{\max} - d_{\min}}
\end{equation}

其中：
\begin{itemize}
\item $d_g$：当前最优个体与种群中其他个体的平均加权PDI距离
\item $d_{\max}$：种群中个体间的最大平均加权PDI距离
\item $d_{\min}$：种群中个体间的最小平均加权PDI距离
\end{itemize}

\subsubsection{动态阈值计算}

为了适应不同网络的特征，算法采用动态阈值划分搜索状态。基于历史$\lambda$值计算四分位数：

\begin{align}
Q_1 &= \text{Percentile}(\Lambda_{history}, 25) \\
Q_3 &= \text{Percentile}(\Lambda_{history}, 75)
\end{align}

其中$\Lambda_{history}$为历史$\lambda$值序列。

\subsubsection{搜索状态划分}

基于$\lambda$值和动态阈值，算法将搜索过程划分为四种状态：

\begin{enumerate}
\item \textbf{收敛状态}（Convergence）：$\lambda \in [0, Q_1)$，种群高度聚集，需要精细搜索
\item \textbf{开发状态}（Exploitation）：$\lambda \in [Q_1, \frac{Q_1+Q_3}{2})$，在有希望区域深入搜索
\item \textbf{探索状态}（Exploration）：$\lambda \in [\frac{Q_1+Q_3}{2}, Q_3)$，扩大搜索范围
\item \textbf{逃逸状态}（Escape）：$\lambda \in [Q_3, 1]$或满足逃逸条件，跳出局部最优
\end{enumerate}

逃逸条件定义为：
\begin{equation}
\text{Escape\_Condition} = (\lambda < 0.1) \land (\text{stagnation\_counter} \geq \text{threshold})
\end{equation}

\subsection{主循环：变异、交叉、选择与参数更新}

LADE算法的主循环包含地形状态分析、状态驱动的变异、交叉、选择和参数自适应更新等关键步骤。

\subsubsection{状态驱动的变异算子}

针对四种搜索状态，算法设计了相应的变异策略：

\paragraph{收敛状态变异算子（DE/best/1）}

在收敛状态下，种群高度聚集，采用基于最优个体的变异策略进行精细搜索：

\begin{algorithm}[H]
\caption{收敛状态变异算子}
\begin{algorithmic}[1]
\REQUIRE 目标个体$X_i$，最优个体$X_{best}$，缩放因子$F$
\ENSURE 变异个体$V_i$
\STATE 随机选择两个不同个体$X_{r1}, X_{r2}$
\STATE 计算差异集$D = X_{r1} \triangle X_{r2}$
\STATE $N_R \leftarrow \lfloor F \times |D| \rfloor$
\STATE $V_i \leftarrow X_{best}$
\FOR{$j = 1$ to $N_R$}
    \IF{$D \neq \emptyset$}
        \STATE 随机选择$node \in D$
        \STATE 找到$V_i$中LFV最小的节点$min\_node$
        \STATE $V_i[V_i.index(min\_node)] \leftarrow node$
        \STATE $D \leftarrow D \setminus \{node\}$
    \ENDIF
\ENDFOR
\RETURN $V_i$
\end{algorithmic}
\end{algorithm}

\paragraph{开发状态变异算子（DE/current-to-best/1）}

在开发状态下，结合当前个体和最优个体信息，平衡局部搜索和全局引导：

\begin{algorithm}[H]
\caption{开发状态变异算子}
\begin{algorithmic}[1]
\REQUIRE 目标个体$X_i$，最优个体$X_{best}$，缩放因子$F$
\ENSURE 变异个体$V_i$
\STATE 随机选择两个不同个体$X_{r1}, X_{r2}$
\STATE 计算差异集$D_1 = X_{best} \setminus X_i$，$D_2 = X_{r1} \triangle X_{r2}$
\STATE 合并差异集$D = D_1 \cup D_2$
\STATE $N_R \leftarrow \lfloor F \times |D| \rfloor$
\STATE $V_i \leftarrow X_i$
\FOR{$j = 1$ to $N_R$}
    \IF{$D \neq \emptyset$}
        \STATE 随机选择$node \in D$
        \STATE 找到$V_i$中LFV最小的节点$min\_node$
        \STATE $V_i[V_i.index(min\_node)] \leftarrow node$
        \STATE $D \leftarrow D \setminus \{node\}$
    \ENDIF
\ENDFOR
\RETURN $V_i$
\end{algorithmic}
\end{algorithm}

\paragraph{探索状态变异算子（DE/rand/1）}

在探索状态下，采用随机变异策略扩大搜索范围：

\begin{algorithm}[H]
\caption{探索状态变异算子}
\begin{algorithmic}[1]
\REQUIRE 目标个体$X_i$，缩放因子$F$
\ENSURE 变异个体$V_i$
\STATE 随机选择三个不同个体$X_{r1}, X_{r2}, X_{r3}$
\STATE 计算差异集$D = X_{r2} \triangle X_{r3}$
\STATE $N_R \leftarrow \lfloor F \times |D| \rfloor$
\STATE $V_i \leftarrow X_{r1}$
\FOR{$j = 1$ to $N_R$}
    \IF{$D \neq \emptyset$}
        \STATE 随机选择$node \in D$
        \STATE 找到$V_i$中LFV最小的节点$min\_node$
        \STATE $V_i[V_i.index(min\_node)] \leftarrow node$
        \STATE $D \leftarrow D \setminus \{node\}$
    \ENDIF
\ENDFOR
\RETURN $V_i$
\end{algorithmic}
\end{algorithm}

\paragraph{逃逸状态变异算子}

在逃逸状态下，采用基于逃逸候选池的扰动变异策略：

\begin{algorithm}[H]
\caption{逃逸状态变异算子}
\begin{algorithmic}[1]
\REQUIRE 目标个体$X_i$，逃逸候选池$\mathcal{E}$
\ENSURE 变异个体$V_i$
\IF{$|\mathcal{E}| \geq 2$}
    \STATE 随机选择$X_1, X_2 \in \mathcal{E}$
    \STATE 计算差异集$D = X_1 \setminus X_2$
    \STATE $V_i \leftarrow X_i$
    \STATE $replace\_count \leftarrow \min(k, |D|)$
    \STATE 随机选择$replace\_count$个位置进行替换
    \FOR{每个选中的位置$pos$}
        \STATE $V_i[pos] \leftarrow D[pos \bmod |D|]$
    \ENDFOR
\ELSE
    \STATE $V_i \leftarrow X_i$ // 候选池不足，返回原个体
\ENDIF
\RETURN $V_i$
\end{algorithmic}
\end{algorithm}

\subsubsection{交叉算子}

算法采用二项式交叉策略：

\begin{algorithm}[H]
\caption{二项式交叉算子}
\begin{algorithmic}[1]
\REQUIRE 目标个体$X_i$，变异个体$V_i$，交叉概率$CR$，种子数$k$
\ENSURE 试验个体$U_i$
\STATE $U_i \leftarrow X_i$
\FOR{$j = 1$ to $k$}
    \IF{$\text{random}() \leq CR$}
        \STATE $U_i[j] \leftarrow V_i[j]$
    \ENDIF
\ENDFOR
\STATE 检查并修复重复节点
\RETURN $U_i$
\end{algorithmic}
\end{algorithm}

\subsubsection{选择算子}

算法采用贪婪选择策略：

\begin{algorithm}[H]
\caption{选择算子}
\begin{algorithmic}[1]
\REQUIRE 目标个体$X_i$，试验个体$U_i$，参数$CR$，$F$
\ENSURE 下一代个体$X_i^{(g+1)}$
\STATE $fitness_{trial} \leftarrow \text{LIE}(U_i)$
\STATE $fitness_{target} \leftarrow \text{LIE}(X_i)$
\IF{$fitness_{trial} \geq fitness_{target}$}
    \STATE 记录成功参数：$S_{CR} \leftarrow S_{CR} \cup \{CR\}$，$S_F \leftarrow S_F \cup \{F\}$
    \RETURN $U_i$
\ELSE
    \RETURN $X_i$
\ENDIF
\end{algorithmic}
\end{algorithm}

\subsubsection{参数自适应更新}

LADE算法采用自适应参数控制策略，动态调整交叉概率$CR$和缩放因子$F$。

\paragraph{参数生成}

交叉概率$CR$服从正态分布：
\begin{equation}
CR \sim \mathcal{N}(\mu_{CR}, 0.1^2)
\end{equation}

缩放因子$F$服从柯西分布：
\begin{equation}
F \sim \text{Cauchy}(\mu_F, 0.1)
\end{equation}

\paragraph{参数更新}

基于成功个体的参数值更新分布参数：

\begin{align}
\mu_{CR}^{(g+1)} &= (1-c) \cdot \mu_{CR}^{(g)} + c \cdot \text{mean}(S_{CR}) \\
\mu_F^{(g+1)} &= (1-c) \cdot \mu_F^{(g)} + c \cdot \text{mean}(S_F)
\end{align}

其中$c=0.1$为学习率，$S_{CR}$和$S_F$为成功参数集合。

\subsection{局部搜索}

为了进一步提升解的质量，LADE算法设计了基于LFV的高效局部搜索机制。

\subsubsection{局部搜索策略}

局部搜索采用基于邻域的贪婪改进策略：

\begin{algorithm}[H]
\caption{基于LFV的局部搜索}
\begin{algorithmic}[1]
\REQUIRE 个体$X$，最大邻居数$max\_neighbors$
\ENSURE 优化后的个体$X'$
\STATE $X' \leftarrow X$，$best\_fitness \leftarrow \text{LIE}(X)$
\STATE $found\_improvement \leftarrow \text{false}$
\STATE 按LFV值升序排序$X$中的节点得到$sorted\_nodes$
\FOR{每个节点$node \in sorted\_nodes$}
    \STATE $neighbors \leftarrow \text{get\_top\_neighbors}(node, max\_neighbors)$
    \FOR{每个邻居$neighbor \in neighbors$}
        \IF{$neighbor \in X'$}
            \STATE \textbf{continue}
        \ENDIF
        \STATE 生成新解：$X_{new}[i] \leftarrow neighbor$ if $X'[i] = node$
        \STATE $new\_fitness \leftarrow \text{LIE}(X_{new})$
        \IF{$new\_fitness > best\_fitness$}
            \STATE $X' \leftarrow X_{new}$，$best\_fitness \leftarrow new\_fitness$
            \STATE $found\_improvement \leftarrow \text{true}$
            \STATE \textbf{break} // 找到改进即跳出
        \ENDIF
    \ENDFOR
\ENDFOR
\RETURN $X'$
\end{algorithmic}
\end{algorithm}

\subsubsection{局部搜索应用}

算法在两个关键位置应用局部搜索：

\begin{enumerate}
\item \textbf{种群优化}：对每代前10\%的优质个体进行局部搜索，参数$max\_neighbors=8$
\item \textbf{全局最优优化}：对当前全局最优个体进行深度局部搜索，参数$max\_neighbors=10$
\end{enumerate}

\subsection{LADE算法完整流程}

\begin{algorithm}[H]
\caption{LADE算法主流程}
\begin{algorithmic}[1]
\REQUIRE 网络图$G=(V,E)$，种子数$k$，种群大小$NP$，最大函数评估次数$FEs_{max}$，激活概率$p$
\ENSURE 最优种子集合$S^*$
\STATE // \textbf{阶段1：预处理}
\STATE $bridge\_nodes \leftarrow \text{detect\_bridge\_nodes}(G)$
\STATE $combined\_scores \leftarrow \text{calculate\_combined\_centrality}(G)$
\STATE $G \leftarrow \text{convert\_node\_labels\_to\_integers}(G)$
\STATE
\STATE // \textbf{阶段2：混合初始化}
\STATE $regions \leftarrow \text{divide\_by\_diameter}(G)$
\STATE $\mathcal{S}_{LHS} \leftarrow \text{sample\_lhs}(G, k, NP/2, bridge\_nodes, combined\_scores)$
\STATE $\mathcal{S}_{score} \leftarrow \text{sample\_score}(G, k, NP/2)$
\STATE $all\_solutions \leftarrow \mathcal{S}_{LHS} \cup \mathcal{S}_{score}$
\STATE $fitness\_cache \leftarrow \text{parallel\_compute\_fitness}(all\_solutions, G, p)$
\STATE $P^{(0)} \leftarrow \text{initialize\_population\_hybrid}(\mathcal{S}_{LHS}, \mathcal{S}_{score}, NP)$
\STATE
\STATE // \textbf{阶段3：参数初始化}
\STATE $\mu_{CR} \leftarrow 0.5$，$\mu_F \leftarrow 0.5$，$c \leftarrow 0.1$
\STATE $\Lambda_{history} \leftarrow \emptyset$，$g \leftarrow 0$，$FEs \leftarrow 0$
\STATE $\lambda^{(0)} \leftarrow \text{compute\_lambda}(P^{(0)})$
\STATE $\Lambda_{history} \leftarrow \Lambda_{history} \cup \{\lambda^{(0)}\}$
\STATE
\STATE // \textbf{阶段4：主进化循环}
\WHILE{$FEs < FEs_{max}$ AND $g < G_{max}$}
    \STATE $g \leftarrow g + 1$
    \STATE $\lambda^{(g)} \leftarrow \text{compute\_lambda}(P^{(g-1)})$
    \STATE $\Lambda_{history} \leftarrow \Lambda_{history} \cup \{\lambda^{(g)}\}$
    \STATE $current\_state \leftarrow \text{determine\_state}(\lambda^{(g)}, \Lambda_{history})$
    \STATE
    \STATE $S_{CR} \leftarrow \emptyset$，$S_F \leftarrow \emptyset$
    \STATE $new\_population \leftarrow \emptyset$
    \FOR{每个个体$X_i \in P^{(g-1)}$}
        \STATE $CR_i, F_i \leftarrow \text{generate\_parameters}(\mu_{CR}, \mu_F)$
        \STATE $V_i \leftarrow \text{select\_mutation}(X_i, current\_state, F_i)$
        \STATE $U_i \leftarrow \text{crossover}(X_i, V_i, CR_i, k)$
        \STATE $X_i^{(g)} \leftarrow \text{selection}(X_i, U_i, CR_i, F_i)$
        \STATE $new\_population \leftarrow new\_population \cup \{X_i^{(g)}\}$
        \STATE 更新$FEs$计数器
    \ENDFOR
    \STATE
    \STATE // \textbf{局部搜索}
    \STATE $num\_to\_optimize \leftarrow \max(1, \lfloor 0.1 \times NP \rfloor)$
    \STATE $candidates \leftarrow \text{top\_individuals}(new\_population, num\_to\_optimize)$
    \FOR{每个候选个体$candidate \in candidates$}
        \STATE $optimized \leftarrow \text{local\_search}(candidate, 8)$
        \IF{$\text{LIE}(optimized) > \text{LIE}(candidate)$}
            \STATE 更新$new\_population$中对应个体
        \ENDIF
    \ENDFOR
    \STATE
    \STATE // \textbf{全局最优局部搜索}
    \STATE $G_{best} \leftarrow \arg\max_{X \in new\_population} \text{LIE}(X)$
    \STATE $G_{best} \leftarrow \text{local\_search}(G_{best}, 10)$
    \STATE
    \STATE // \textbf{参数更新}
    \IF{$S_{CR} \neq \emptyset$ AND $S_F \neq \emptyset$}
        \STATE $\mu_{CR} \leftarrow (1-c) \cdot \mu_{CR} + c \cdot \text{mean}(S_{CR})$
        \STATE $\mu_F \leftarrow (1-c) \cdot \mu_F + c \cdot \text{mean}(S_F)$
    \ENDIF
    \STATE
    \STATE $P^{(g)} \leftarrow new\_population$
\ENDWHILE
\STATE
\RETURN $S^* \leftarrow \arg\max_{X \in P^{(g)}} \text{LIE}(X)$
\end{algorithmic}
\end{algorithm}

\subsection{算法复杂度分析}

\subsubsection{时间复杂度}

LADE算法的时间复杂度主要由以下几个部分构成：

\begin{enumerate}
\item \textbf{预处理阶段}：
   \begin{itemize}
   \item 桥节点检测：$O(n \cdot m)$（介数中心性计算）
   \item 网络区域划分：$O(n^2)$（最短路径计算）
   \item 节点ID连续化：$O(n + m)$
   \end{itemize}

\item \textbf{初始化阶段}：
   \begin{itemize}
   \item LHS采样：$O(SN \cdot k)$
   \item 度中心性采样：$O(SN \cdot k)$
   \item 并行适应度计算：$O(SN \cdot k \cdot \bar{d})$，其中$\bar{d}$为平均度
   \item 质量与多样性筛选：$O(SN^2 \cdot k)$
   \end{itemize}

\item \textbf{主进化循环}（每代）：
   \begin{itemize}
   \item 地形状态值计算：$O(NP^2 \cdot k)$（距离矩阵计算）
   \item 变异操作：$O(NP \cdot k)$
   \item 交叉操作：$O(NP \cdot k)$
   \item 选择操作：$O(NP \cdot k \cdot \bar{d})$（适应度评估）
   \item 局部搜索：$O(0.1 \cdot NP \cdot k \cdot max\_neighbors \cdot \bar{d})$
   \end{itemize}
\end{enumerate}

总时间复杂度为：
\begin{equation}
T(n,k,NP,G_{max}) = O(n^2 + G_{max} \cdot NP \cdot (NP \cdot k + k \cdot \bar{d}))
\end{equation}

在实际应用中，由于采用了适应度缓存机制和并行计算，实际运行时间显著优于理论复杂度。

\subsubsection{空间复杂度}

算法的空间复杂度主要包括：
\begin{itemize}
\item 种群存储：$O(NP \cdot k)$
\item 网络存储：$O(n + m)$，其中$m$为边数
\item LFV缓存：$O(n)$
\item 适应度缓存：$O(C)$，其中$C$为缓存大小
\item 距离矩阵：$O(NP^2)$
\item 辅助数据结构：$O(n + k)$
\end{itemize}

总空间复杂度为：$O(NP^2 + NP \cdot k + n + m + C)$

\subsection{本章小结}

本章详细介绍了基于地形状态感知的自适应差分进化算法（LADE）。该算法针对影响力最大化问题的特点，设计了完整的求解框架：

\begin{enumerate}
\item \textbf{问题建模}：采用整数向量编码方案，将影响力最大化问题转化为组合优化问题

\item \textbf{混合初始化}：结合LHS采样和度中心性采样，通过质量与多样性筛选生成高质量初始种群

\item \textbf{适应度评估}：采用LIE函数进行快速准确的影响力估计，配合缓存机制提高计算效率

\item \textbf{地形状态感知}：通过加权PDI距离计算地形状态值$\lambda$，实现搜索状态的实时感知和动态阈值划分

\item \textbf{状态驱动的主循环}：基于四种搜索状态自适应选择变异算子，配合二项式交叉和贪婪选择，实现高效的种群进化

\item \textbf{参数自适应}：采用正态分布和柯西分布生成CR和F参数，基于成功经验进行参数更新

\item \textbf{局部搜索}：对优质个体进行基于LFV的邻域搜索，进一步提升解的质量
\end{enumerate}

算法的主要创新点包括：
\begin{itemize}
\item 首次将适应度地形理论引入影响力最大化问题，实现搜索状态的量化感知
\item 设计了基于LFV的加权PDI距离度量，更准确地刻画解空间结构
\item 提出了四态搜索机制，实现搜索策略的自适应调整
\item 集成了高效的局部搜索机制，平衡了全局探索和局部开发
\end{itemize}

通过理论分析和算法设计，LADE算法为解决影响力最大化问题提供了一种新的有效途径，具有良好的理论基础和实用价值。

\end{document}
