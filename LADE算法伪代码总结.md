# LADE算法伪代码总结

基于您的实际代码实现，以下是LADE算法的详细伪代码：

## 主算法流程

```pseudocode
Algorithm: LADE (Landscape-Aware Adaptive Differential Evolution)
Input: G=(V,E), k, pop_size, max_gen, FEs_max, p, SN
Output: Best seed set S*

1. // ========== 预处理阶段 ==========
2. bridge_nodes ← detect_bridge_nodes(G)
3. combined_scores ← calculate_combined_centrality_igraph(G)
4. G ← convert_node_labels_to_integers(G)  // 节点ID连续化
5. 
6. // ========== 初始化阶段 ==========
7. regions ← divide_by_diameter(G)
8. lhs_solutions ← sample_lhs(G, k, SN/2, bridge_nodes, combined_scores)
9. score_solutions ← sample_score(G, k, SN/2)
10. 
11. // 并行预计算所有候选解的适应度
12. all_solutions ← lhs_solutions + score_solutions
13. fitness_cache ← parallel_compute_fitness(all_solutions, G, p)
14. 
15. // 质量筛选和多样性筛选
16. high_quality ← quality_filter(all_solutions, fitness_cache, pop_size*0.7)
17. diverse_solutions ← diversity_filter(high_quality, similarity_threshold=0.8)
18. initial_population ← diverse_solutions[:pop_size]
19. 
20. // ========== ANFDE实例化 ==========
21. anfde ← ANFDE(G, p, bridge_nodes, k)
22. initial_lambda ← anfde.compute_lambda(initial_population)
23. initial_state ← anfde.determine_state(initial_lambda, initialization=True)
24. 
25. // ========== 主进化循环 ==========
26. population ← initial_population
27. gen ← 0
28. FEs ← 0
29. mu_CR ← 0.5, mu_F ← 0.5, c ← 0.1
30. lambda_history ← [initial_lambda]
31. fitness_history ← []
32. state_history ← [initial_state]
33. 
34. while FEs < FEs_max and gen < max_gen:
35.     gen ← gen + 1
36.     
37.     // ========== 地形状态分析 ==========
38.     lambda_val ← compute_lambda(population)
39.     lambda_history.append(lambda_val)
40.     current_state ← determine_state(lambda_val)
41.     state_history.append(current_state)
42.     
43.     // ========== 选择变异算子 ==========
44.     mutation_op ← select_mutation_operator(current_state)
45.     
46.     // ========== 种群进化 ==========
47.     new_population ← []
48.     success_CR ← []
49.     success_F ← []
50.     
51.     for each individual X_i in population:
52.         // 参数生成
53.         CR_i ← Normal(mu_CR, 0.1²)  // 截断到[0,1]
54.         F_i ← Cauchy(mu_F, 0.1)     // 截断到[0,1]
55.         
56.         // 变异操作
57.         V_i ← apply_mutation(X_i, mutation_op, F_i, current_state)
58.         
59.         // 交叉操作
60.         U_i ← uniform_crossover(X_i, V_i, CR_i, k)
61.         
62.         // 选择操作
63.         if fitness(U_i) >= fitness(X_i):
64.             new_population.append(U_i)
65.             success_CR.append(CR_i)
66.             success_F.append(F_i)
67.             FEs ← FEs + 1  // 适应度评估计数
68.         else:
69.             new_population.append(X_i)
70.     
71.     population ← new_population
72.     
73.     // ========== 参数更新 ==========
74.     if success_CR is not empty and success_F is not empty:
75.         mu_CR ← (1-c) * mu_CR + c * lehmer_mean(success_CR)
76.         mu_F ← (1-c) * mu_F + c * lehmer_mean(success_F)
77.     
78.     // 记录最佳适应度
79.     best_fitness ← max(fitness(X) for X in population)
80.     fitness_history.append(best_fitness)
81. 
82. // 返回最优解
83. best_individual ← argmax(fitness(X) for X in population)
84. return best_individual
```

## 关键子算法

### 1. 地形状态值计算

```pseudocode
Function: compute_lambda(population)
Input: population
Output: lambda value

1. if len(population) < 2:
2.     return 0.5
3. 
4. // 批量计算适应度
5. fitness_values ← fitness_batch(population)
6. best_idx ← argmax(fitness_values)
7. 
8. // 向量化距离矩阵计算
9. distance_matrix ← compute_distance_matrix_vectorized(population)
10. avg_distances ← mean(distance_matrix, axis=1)
11. 
12. d_g ← avg_distances[best_idx]
13. d_max ← max(avg_distances)
14. d_min ← min(avg_distances)
15. 
16. if d_max == d_min:
17.     return 0.5
18. 
19. lambda ← (d_g - d_min) / (d_max - d_min)
20. return lambda
```

### 2. 状态判断

```pseudocode
Function: determine_state(lambda_val)
Input: lambda_val, generation
Output: search state

1. // 前10代强制探索
2. if generation < 10:
3.     return 'exploration'
4. 
5. // 检查逃逸条件
6. if check_escape_condition(lambda_val, current_fitness):
7.     return 'escape'
8. 
9. // 计算动态阈值
10. Q1, Q3 ← compute_dynamic_thresholds(lambda_history)
11. 
12. // 状态划分
13. if lambda_val in [0.0, Q1):
14.     return 'convergence'
15. elif lambda_val in [Q1, (Q1+Q3)/2):
16.     return 'exploitation'
17. elif lambda_val in [(Q1+Q3)/2, Q3):
18.     return 'exploration'
19. else:  // lambda_val in [Q3, 1.0]
20.     return 'escape'
```

### 3. 变异算子选择

```pseudocode
Function: apply_mutation(individual, mutation_op, F, state)
Input: individual, mutation_operator, F_value, current_state
Output: mutant individual

1. switch mutation_op:
2.     case 'convergence':
3.         return convergence_mutation(individual, F)  // DE/best/1
4.     case 'exploitation':
5.         return exploitation_mutation(individual, F)  // DE/current-to-best/1
6.     case 'exploration':
7.         return exploration_mutation(individual, F)   // DE/rand/1
8.     case 'escape':
9.         return escape_optimization(individual, F)    // 基于网络结构的优化
```

### 4. 收敛状态变异（DE/best/1）

```pseudocode
Function: convergence_mutation(individual, F)
Input: individual X_i, scaling factor F
Output: mutant individual V_i

1. X_best ← get_best_individual(population)
2. X_r1, X_r2 ← select_distinct_individuals(X_best, 2)
3. 
4. difference_set ← symmetric_difference(X_r1, X_r2)
5. num_replacements ← floor(F * len(difference_set))
6. 
7. V_i ← copy(X_best)
8. for j in range(num_replacements):
9.     if difference_set is not empty:
10.         node_to_replace ← random_choice(difference_set)
11.         min_influence_node ← min(V_i, key=lambda n: F_cache[n])
12.         V_i[V_i.index(min_influence_node)] ← node_to_replace
13.         difference_set.remove(node_to_replace)
14. 
15. return V_i
```

### 5. 逃逸状态优化

```pseudocode
Function: escape_optimization(individual, F)
Input: individual X_i, scaling factor F
Output: optimized individual V_i

1. NR ← min(floor(F * k), k)
2. V_i ← copy(individual)
3. 
4. // 按F值排序节点（从低到高）
5. sorted_nodes ← sort(V_i, key=lambda n: F_cache[n])
6. 
7. // 获取候选节点：高度节点和桥节点
8. degree_threshold ← percentile([degree(n) for n in all_nodes], 75)
9. candidate_nodes ← [n for n in all_nodes 
10.                    if n not in V_i and 
11.                       (n in bridge_nodes or degree(n) > degree_threshold)]
12. 
13. // 替换操作
14. for j in range(NR):
15.     if candidate_nodes is not empty:
16.         low_node ← sorted_nodes[j % len(sorted_nodes)]
17.         candidate ← random_choice(candidate_nodes)
18.         V_i[V_i.index(low_node)] ← candidate
19. 
20. return V_i
```

### 6. 均匀交叉

```pseudocode
Function: uniform_crossover(target, mutant, CR, k)
Input: target individual X_i, mutant V_i, crossover rate CR, seed size k
Output: trial individual U_i

1. U_i ← empty_set()
2. j_rand ← random_integer(1, k)
3. 
4. for j in range(1, k+1):
5.     if random() <= CR or j == j_rand:
6.         U_i.add(V_i[j])
7.     else:
8.         U_i.add(X_i[j])
9. 
10. // 处理重复节点
11. U_i ← handle_duplicates(U_i, k, all_nodes)
12. 
13. return U_i
```

### 7. LIE适应度评估

```pseudocode
Function: LIE_two_hop(seed_set, G, p)
Input: seed set S, graph G, probability p
Output: influence estimation

1. // 种子节点贡献
2. influence ← len(seed_set)
3. 
4. // 一跳邻居贡献
5. one_hop_neighbors ← get_one_hop_neighbors(seed_set, G) - seed_set
6. for v in one_hop_neighbors:
7.     seed_neighbors ← seed_set ∩ neighbors(v)
8.     prob ← 1 - product((1-p) for u in seed_neighbors)
9.     influence ← influence + prob
10. 
11. // 二跳邻居贡献
12. two_hop_neighbors ← get_two_hop_neighbors(seed_set, G) - seed_set - one_hop_neighbors
13. for v in two_hop_neighbors:
14.     connecting_one_hop ← one_hop_neighbors ∩ neighbors(v)
15.     prob ← 1 - product((1 - p * P1(u)) for u in connecting_one_hop)
16.     influence ← influence + prob
17. 
18. return influence
```

## 关键数据结构

```pseudocode
Class: ANFDE
    Attributes:
        - G: 网络图
        - p: 激活概率
        - k: 种子数量
        - population: 当前种群
        - bridge_nodes: 桥节点集合
        - F_cache: 节点影响力缓存 {node: F_value}
        - mu_CR, mu_F: 参数分布均值
        - lambda_history: λ值历史
        - fitness_history: 适应度历史
        - state_history: 状态历史
        - stagnation_counter: 停滞计数器
        
    Methods:
        - compute_lambda(population)
        - determine_state(lambda_val)
        - apply_mutation(individual, state, F)
        - uniform_crossover(target, mutant, CR)
        - fitness(individual)
        - generate_parameters()
        - update_parameters(success_CR, success_F)
```

## 算法复杂度

- **时间复杂度**: O(n² + G_max × NP × (NP × k + k × d̄))
- **空间复杂度**: O(NP × k + n + m + C)

其中：
- n: 节点数, m: 边数, k: 种子数
- NP: 种群大小, G_max: 最大代数
- d̄: 平均度, C: 缓存大小
