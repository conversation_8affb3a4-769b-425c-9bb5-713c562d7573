import random
import networkx as nx
import igraph as ig
import community  # python-louvain库
from pyDOE import lhs
from sklearn.preprocessing import MinMaxScaler
import numpy as np
from concurrent.futures import ProcessPoolExecutor  # 并行计算
from functools import partial




#======================直径划分======================
def divide_by_diameter(G):
    """
    基于网络的直径路径将节点划分为多个区域。
    参数:
        G : networkx.Graph, 输入网络图 (可以是非连通图).
    返回:
        regions : dict, 每个区域对应的节点集合（根据与直径路径的距离划分）。
                如果图为空或没有边，则返回空字典。
    """

    if not G.nodes() or not G.edges():  # 处理空图或没有边的情况
        return {}

    # 确保所有节点都是整数型, 并且是连续的整数
    G = nx.convert_node_labels_to_integers(G)

    # 获取最大连通子图
    largest_cc = max(nx.connected_components(G), key=len)
    G_lcc = G.subgraph(largest_cc).copy()  # 使用子图的副本

    # 计算直径端点：随机选择一个起点 u (在最大连通子图上)
    u = random.choice(list(G_lcc.nodes()))
    distances = nx.single_source_shortest_path_length(G_lcc, u)
    v = max(distances.items(), key=lambda x: x[1])[0]

    # 以 v 为起点，找到直径的另一端 u (在最大连通子图上)
    distances = nx.single_source_shortest_path_length(G_lcc, v)
    u = max(distances.items(), key=lambda x: x[1])[0]

    # 计算从 u 到 v 的最短路径 (在最大连通子图上)
    try:  #  处理u和v相同的情况，即只有一个节点的连通子图
        diameter_path = nx.shortest_path(G_lcc, source=u, target=v)
    except nx.NetworkXNoPath: #如果u和v相同，则diameter_path只有u一个节点
        diameter_path = [u]


    regions = {0: set(diameter_path)}  #  直径路径上的节点已经是整数
    for node in G_lcc.nodes():  # 只在最大连通子图上遍历
        if node not in regions[0]:
            try:
                dist = min(nx.shortest_path_length(G_lcc, node, p) for p in diameter_path)
            except nx.NetworkXNoPath:
                # 处理最大连通子图中仍可能存在的NoPath情况（例如孤立节点）
                continue  # 跳过这个节点

            if dist not in regions:
                regions[dist] = set()
            regions[dist].add(node)

    return regions


"""基于介数中心性的桥接节点检测（近似）"""
def detect_bridge_nodes(G, top_percent=0.1):
    if G.number_of_nodes() == 0:
        return []

    # 对于小图，使用精确计算；对于大图，使用采样
    if G.number_of_nodes() <= 100:
        betweenness = nx.betweenness_centrality(G)
    else:
        k_sample = min(100, G.number_of_nodes() // 2)
        betweenness = nx.betweenness_centrality(G, k=k_sample)

    if not betweenness:
        return list(G.nodes())[:max(1, int(len(G.nodes()) * top_percent))]

    sorted_nodes = sorted(betweenness, key=betweenness.get, reverse=True)
    num_bridge_nodes = max(1, int(len(G.nodes()) * top_percent))
    return sorted_nodes[:num_bridge_nodes]


# ===============计算综合中心性得分（优化归一化）版本1=====================
def calculate_combined_centrality_igraph(G, weights=[0.6, 0.4]):
    """计算综合中心性得分（igraph版本）"""
    # 创建igraph图
    ig_G = ig.Graph()
    node_list = list(G.nodes())
    ig_G.add_vertices(len(node_list))
    ig_G.vs["name"] = node_list
    edge_list = [(node_list.index(u), node_list.index(v)) for u, v in G.edges()]
    ig_G.add_edges(edge_list)


    # 计算中心性
    betweenness = ig_G.betweenness()
    degree = ig_G.degree()

    # 归一化处理
    scaler = MinMaxScaler()
    combined = np.array([[betweenness[i], degree[i]] for i in range(len(betweenness))])
    combined_norm = scaler.fit_transform(combined)

    # 加权中心性得分，确保键为整数
    scores = {int(node_list[i]): np.dot(combined_norm[i], weights) for i in range(len(node_list))}
    return scores


#  =============PageRank 和结构洞系数的加权平均值，节点的全局影响力和局部桥接能力版本2======================

# def calculate_constraint(G, node):
#     """计算单个节点的结构洞约束系数"""
#     neighbors = list(G.neighbors(node))
#     if not neighbors:
#         return 0
#     degrees = [G.degree(neighbor) for neighbor in neighbors]
#     total_degree = sum(degrees)
#     if total_degree == 0:
#         return 0
#     proportion = [degree / total_degree for degree in neighbors]  # Corrected the typo here
#     return sum([(p + p**2) for p in proportion])

#
# def calculate_node_weights_igraph(G, alpha_initial=0.6, alpha_final=0.3, coverage=0.75, n_jobs=4):
#     # 创建igraph图
#     ig_G = ig.Graph()
#
#     # 先添加节点
#     node_list = list(G.nodes())
#     ig_G.add_vertices(len(node_list))
#     ig_G.vs["name"] = node_list
#
#     # 再添加边
#     edge_list = [(node_list.index(u), node_list.index(v)) for u, v in G.edges()]
#     ig_G.add_edges(edge_list)
#
#     # 计算PageRank
#     pagerank_scores = ig_G.pagerank()
#     pagerank_dict = dict(zip(node_list, pagerank_scores))
#
#     # 计算结构洞约束系数
#     with ProcessPoolExecutor(max_workers=n_jobs) as executor:
#         constraint_scores = list(executor.map(partial(calculate_constraint, G), node_list))
#
#     constraint_dict = dict(zip(node_list, constraint_scores))
#     sh_scores = {node: 1 / constraint_dict[node] if constraint_dict[node] > 0 else 0 for node in node_list}
#
#     # 计算alpha值
#     alpha = alpha_initial if coverage <= 0.7 else alpha_initial - (alpha_initial - alpha_final) * (coverage - 0.7) / 0.3
#
#     # 计算最终权重
#     weights = {node: alpha * pagerank_dict[node] + (1 - alpha) * sh_scores[node] for node in node_list}
#     return weights




# 计算解的相似度
def solution_similarity(sol1, sol2):
    set1 = set(sol1)
    set2 = set(sol2)
    intersection = len(set1.intersection(set2))
    union = len(set1.union(set2))
    return intersection / union if union > 0 else 0

"""三阶段严格优先级补充策略（优化版，带节点补充详情打印）"""
def strict_supplement_with_validation(G, current_solution, num_needed, regions, bridge_nodes, combined_scores):
    current_solution_set = set(map(int, current_solution))  # 确保当前解中的节点是整数
    supplement = []
    remaining = num_needed

    # 阶段 1：从每个区域选最高分节点（仅一轮轮询）
    sorted_regions = sorted(regions.keys())
    stage1_selected = []
    # print("阶段1开始：")
    for r in sorted_regions:
        if remaining <= 0:
            break
        candidates = [int(n) for n in regions[r] if int(n) not in current_solution_set]  # 确保节点是整数
        if candidates:
            # 从候选节点中选择得分最高的节点，如果 combined_scores 中缺少键，则跳过
            try:
                selected = max(candidates, key=lambda x: combined_scores[x])
                stage1_selected.append((r, selected))
                current_solution_set.add(selected)
                remaining -= 1
                # print(f"区域R{r}中选择节点{selected}")
            except KeyError as e:
                print(f"KeyError: combined_scores 中缺少键 {e}, 跳过该节点")  # 打印错误信息
                continue  # 跳过此节点


    # 阶段 2：桥节点补充
    stage2_selected = []
    # print("阶段2开始：")
    if remaining > 0:
        bridge_nodes = list(map(int, bridge_nodes))  # 确保桥节点是整数
        bridge_scores = [combined_scores.get(node, -1)  # 使用 get 方法，如果键不存在返回 -1
                         for node in bridge_nodes if node not in current_solution_set]

        if bridge_scores:
            threshold = np.mean(bridge_scores) if bridge_scores else 0 #避免空列表求平均值的情况
            selected_bridges = [node for node in bridge_nodes
                                if combined_scores.get(node, -1) > threshold # 使用get方法并设置默认值
                                and node not in current_solution_set]

            selected_bridges.sort(key=lambda x: combined_scores.get(x, -1), reverse=True) # 使用get方法并设置默认值

            for bridge in selected_bridges:
                if remaining <= 0:
                    break
                stage2_selected.append(bridge)
                current_solution_set.add(bridge)
                remaining -= 1
                # print(f"桥节点补充节点 {bridge}")


    # 阶段 3：外围节点补充
    stage3_selected = []
    # print("阶段3开始：")
    if remaining > 0:
        candidates = [
            int(n) for n in G.nodes()
            if int(n) not in current_solution_set
            and int(n) not in [x[1] for x in stage1_selected]
            and int(n) not in stage2_selected
            and all(int(n) not in regions[r] for r in regions) # 修改：使用all()来判断是否不在所有区域中
        ]

        if candidates: #判断候选集是否为空
            sorted_candidates = sorted(candidates, key=lambda x: combined_scores.get(x,-1), reverse=True)[:remaining]
            stage3_selected.extend(sorted_candidates)
            for node in sorted_candidates:
                print(f"外围节点补充节点 {node}")




    supplement = [n for r, n in stage1_selected] + stage2_selected + stage3_selected

    # print("\n补充节点详情：")
    # for r, n in stage1_selected:
    #     print(f"  区域 R{r} 补充节点 {n}，得分：{combined_scores.get(n, 'N/A'):.4f}") # 使用get并处理缺失值
    # for n in stage2_selected:
    #     print(f"  桥节点补充节点 {n}，得分：{combined_scores.get(n, 'N/A'):.4f}") # 使用get并处理缺失值
    # for n in stage3_selected:
    #     print(f"  外围节点补充节点 {n}，得分：{combined_scores.get(n, 'N/A'):.4f}")# 使用get并处理缺失值


    return supplement





